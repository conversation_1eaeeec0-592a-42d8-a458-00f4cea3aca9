#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <errno.h>
#include <fcntl.h>
#include <time.h>
#include <sys/stat.h>
#include <stdarg.h>
#include <signal.h>
#include <sys/time.h>
#include <dirent.h>
#include <limits.h>
#include <libgen.h>

#ifndef PATH_MAX
#define PATH_MAX 4096
#endif

#define CONFIG_FILE "server_tester.ini"
#define MAX_LINE_LENGTH 256
#define LOG_DIR "logs"
#define MAX_LOG_PATH 512
#define MAX_MESSAGES 100
#define MAX_FILENAME_LENGTH 256
#define HEALTH_CHECK_INTERVAL 30  // seconds
#define MAX_CONSECUTIVE_FAILURES 3

// Structure pour stocker la configuration
typedef struct {
    char server_ip[16];
    int server_port;
    int ack_timeout;
    int max_ack_size;
    int max_buffer_size;
    int test_interval_hours;
    int health_check_enabled;
    int max_retry_attempts;
    int retry_delay_seconds;
    char messages_directory[256];
    char alert_log_file[256];
} Config;

// Structure pour stocker les informations d'un message de test
typedef struct {
    char filename[MAX_FILENAME_LENGTH];
    char filepath[MAX_FILENAME_LENGTH];
    size_t size;
} TestMessage;

// Structure pour les statistiques de monitoring
typedef struct {
    int total_tests;
    int successful_tests;
    int failed_tests;
    int consecutive_failures;
    time_t last_success;
    time_t last_failure;
    time_t start_time;
} MonitoringStats;

// Variables globales pour la gestion des signaux
volatile sig_atomic_t keep_running = 1;
volatile sig_atomic_t reload_config = 0;

// Déclarations de fonctions
void log_message(const char* level, const char* format, ...);
void log_alert(const char* format, ...);
int init_logging(const char* alert_log_path);
void close_logging();
char* trim(char* str);
int load_config(Config* config, const char* filename);
void error_exit(const char* message);
int load_test_messages(const char* directory, TestMessage* messages, int max_messages);
int analyze_ack_response(const char* ack_buffer, size_t ack_size);
int perform_single_test(const Config* config, const TestMessage* message);
int perform_health_check(const Config* config);
void update_stats(int test_result);
void print_stats();
void signal_handler(int sig);
void setup_signal_handlers();
int validate_file_path(const char* path, const char* base_dir);
char* sanitize_path(const char* path);

// Global log file pointer
FILE* log_file = NULL;
FILE* alert_log_file = NULL;
MonitoringStats stats = {0};

// Gestionnaires de signaux
void signal_handler(int sig) {
    switch(sig) {
        case SIGINT:
        case SIGTERM:
            log_message("INFO", "Received termination signal %d, shutting down gracefully", sig);
            keep_running = 0;
            break;
        case SIGHUP:
            log_message("INFO", "Received SIGHUP signal, reloading configuration");
            reload_config = 1;
            break;
        default:
            log_message("WARNING", "Received unexpected signal %d", sig);
            break;
    }
}

// Initialiser les gestionnaires de signaux
void setup_signal_handlers() {
    struct sigaction sa;
    sa.sa_handler = signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;

    sigaction(SIGINT, &sa, NULL);
    sigaction(SIGTERM, &sa, NULL);
    sigaction(SIGHUP, &sa, NULL);

    // Ignorer SIGPIPE pour éviter les crashes sur les connexions fermées
    signal(SIGPIPE, SIG_IGN);
}

// Initialize logging system
int init_logging(const char* alert_log_path) {
    time_t now;
    struct tm* tm_info;
    char log_filename[MAX_LOG_PATH];
    char timestamp[20];
    char current_dir[PATH_MAX];

    // Get current working directory for path validation
    if (getcwd(current_dir, sizeof(current_dir)) == NULL) {
        fprintf(stderr, "Error: Failed to get current directory: %s\n", strerror(errno));
        return -1;
    }

    // Create a logs directory if it doesn't exist
    struct stat st = {0};
    if (stat(LOG_DIR, &st) == -1) {
        if (mkdir(LOG_DIR, 0755) != 0) {
            fprintf(stderr, "Error: Failed to create logs directory: %s\n", strerror(errno));
            return -1;
        }
    }

    // Get current date for log filename
    time(&now);
    tm_info = localtime(&now);
    strftime(timestamp, sizeof(timestamp), "%Y%m%d", tm_info);

    // Create log filename
    snprintf(log_filename, sizeof(log_filename), "%s/server_tester_%s.log", LOG_DIR, timestamp);

    // Open log file in append mode
    log_file = fopen(log_filename, "a");
    if (log_file == NULL) {
        fprintf(stderr, "Error: Failed to open log file '%s': %s\n", log_filename, strerror(errno));
        return -1;
    }

    // Open alert log file if specified with security validation
    if (alert_log_path && strlen(alert_log_path) > 0) {
        // Sanitize the alert log path to prevent path traversal
        char* sanitized_alert_path = sanitize_path(alert_log_path);

        if (sanitized_alert_path == NULL) {
            fprintf(stderr, "Warning: Invalid alert log path '%s', skipping alert logging\n", alert_log_path);
            return 0;
        }

        // Additional validation: ensure the path doesn't go outside reasonable bounds
        // Allow paths starting with '/' (absolute) or relative paths within current directory
        if (strstr(sanitized_alert_path, "..") != NULL) {
            fprintf(stderr, "Warning: Alert log path '%s' contains directory traversal, skipping alert logging\n", alert_log_path);
            return 0;
        }

        // Create directory for alert log if it doesn't exist
        char* alert_dir = dirname(strdup(sanitized_alert_path));
        if (alert_dir && strlen(alert_dir) > 0 && strcmp(alert_dir, ".") != 0) {
            struct stat alert_st = {0};
            if (stat(alert_dir, &alert_st) == -1) {
                if (mkdir(alert_dir, 0755) != 0) {
                    fprintf(stderr, "Warning: Failed to create alert log directory '%s': %s\n", alert_dir, strerror(errno));
                    // Continue without alert logging
                    return 0;
                }
            }
        }

        alert_log_file = fopen(sanitized_alert_path, "a");
        if (alert_log_file == NULL) {
            fprintf(stderr, "Warning: Failed to open alert log file '%s': %s\n", sanitized_alert_path, strerror(errno));
            // Continue without alert logging
        }
    }

    return 0;
}

// Log a message with timestamp
void log_message(const char* level, const char* format, ...) {
    if (log_file == NULL) return;

    time_t now;
    struct tm* tm_info;
    char timestamp[32];
    va_list args;

    // Get current timestamp
    time(&now);
    tm_info = localtime(&now);
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);

    // Write timestamp and level to log
    fprintf(log_file, "[%s] [%s] ", timestamp, level);

    // Write formatted message
    va_start(args, format);
    vfprintf(log_file, format, args);
    va_end(args);

    fprintf(log_file, "\n");
    fflush(log_file);
}

// Log an alert message (both to regular log and alert log)
void log_alert(const char* format, ...) {
    time_t now;
    struct tm* tm_info;
    char timestamp[32];
    va_list args;

    // Get current timestamp
    time(&now);
    tm_info = localtime(&now);
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);

    // Log to regular log
    if (log_file != NULL) {
        fprintf(log_file, "[%s] [ALERT] ", timestamp);
        va_start(args, format);
        vfprintf(log_file, format, args);
        va_end(args);
        fprintf(log_file, "\n");
        fflush(log_file);
    }

    // Log to alert log
    if (alert_log_file != NULL) {
        fprintf(alert_log_file, "[%s] [ALERT] ", timestamp);
        va_start(args, format);
        vfprintf(alert_log_file, format, args);
        va_end(args);
        fprintf(alert_log_file, "\n");
        fflush(alert_log_file);
    }

    // Also print to stderr for immediate visibility
    fprintf(stderr, "[%s] [ALERT] ", timestamp);
    va_start(args, format);
    vfprintf(stderr, format, args);
    va_end(args);
    fprintf(stderr, "\n");
}

// Close logging system
void close_logging() {
    if (log_file != NULL) {
        fclose(log_file);
        log_file = NULL;
    }
    if (alert_log_file != NULL) {
        fclose(alert_log_file);
        alert_log_file = NULL;
    }
}

// Function to trim whitespace from string
char* trim(char* str) {
    char* end;

    // Remove leading spaces
    while (*str == ' ' || *str == '\t') str++;

    if (*str == 0) return str;

    // Remove trailing spaces
    end = str + strlen(str) - 1;
    while (end > str && (*end == ' ' || *end == '\t' || *end == '\n' || *end == '\r')) end--;

    end[1] = '\0';
    return str;
}

// Function to sanitize file path and prevent path traversal attacks
char* sanitize_path(const char* path) {
    if (path == NULL) return NULL;

    static char sanitized[PATH_MAX];

    // Copy the path and manually sanitize it
    strncpy(sanitized, path, sizeof(sanitized) - 1);
    sanitized[sizeof(sanitized) - 1] = '\0';

    // Remove any "../" sequences to prevent directory traversal
    char* pos;
    while ((pos = strstr(sanitized, "../")) != NULL) {
        memmove(pos, pos + 3, strlen(pos + 3) + 1);
    }

    // Remove any "./" sequences at the beginning
    while (strncmp(sanitized, "./", 2) == 0) {
        memmove(sanitized, sanitized + 2, strlen(sanitized + 2) + 1);
    }

    return sanitized;
}

// Function to validate file path against a base directory
int validate_file_path(const char* path, const char* base_dir) {
    if (path == NULL || base_dir == NULL) {
        return 0; // Invalid input
    }

    char* sanitized_path = sanitize_path(path);
    char* sanitized_base = sanitize_path(base_dir);

    if (sanitized_path == NULL || sanitized_base == NULL) {
        return 0; // Sanitization failed
    }

    // Check if the sanitized path starts with the base directory
    size_t base_len = strlen(sanitized_base);
    if (strncmp(sanitized_path, sanitized_base, base_len) != 0) {
        return 0; // Path is outside base directory
    }

    // Additional security checks
    // Reject paths containing null bytes
    if (strlen(path) != strcspn(path, "\0")) {
        return 0;
    }

    // Reject paths that are too long
    if (strlen(path) >= PATH_MAX) {
        return 0;
    }

    return 1; // Path is valid
}

// Function to load configuration from INI file
int load_config(Config* config, const char* filename) {
    FILE* file;
    char line[MAX_LINE_LENGTH];
    char* key;
    char* value;
    char* equals_pos;

    // Initialize config structure with default values
    memset(config, 0, sizeof(Config));
    config->test_interval_hours = 1;  // Default: test every hour
    config->health_check_enabled = 1; // Default: health check enabled
    config->max_retry_attempts = 3;   // Default: 3 retry attempts
    config->retry_delay_seconds = 5;  // Default: 5 seconds between retries
    strcpy(config->messages_directory, "messages"); // Default messages directory
    strcpy(config->alert_log_file, "logs/alerts.log"); // Default alert log file

    file = fopen(filename, "r");
    if (file == NULL) {
        log_message("ERROR", "Configuration file '%s' not found: %s", filename, strerror(errno));
        fprintf(stderr, "Error: Configuration file '%s' not found: %s\n", filename, strerror(errno));
        return -1;
    }

    log_message("INFO", "Loading configuration from file: %s", filename);

    while (fgets(line, sizeof(line), file)) {
        // Remove comments
        char* comment_pos = strchr(line, '#');
        if (comment_pos != NULL) {
            *comment_pos = '\0';
        }

        // Find '=' sign
        equals_pos = strchr(line, '=');
        if (equals_pos == NULL) {
            continue; // Line without '=', ignore
        }

        // Split key and value
        *equals_pos = '\0';
        key = trim(line);
        value = trim(equals_pos + 1);

        // Ignore empty lines
        if (strlen(key) == 0 || strlen(value) == 0) {
            continue;
        }

        // Parse different keys
        if (strcmp(key, "server_ip") == 0) {
            strncpy(config->server_ip, value, sizeof(config->server_ip) - 1);
            config->server_ip[sizeof(config->server_ip) - 1] = '\0';
            log_message("INFO", "Configuration loaded: server_ip = %s", config->server_ip);
        }
        else if (strcmp(key, "server_port") == 0) {
            config->server_port = atoi(value);
            log_message("INFO", "Configuration loaded: server_port = %d", config->server_port);
        }
        else if (strcmp(key, "ack_timeout") == 0) {
            config->ack_timeout = atoi(value);
            log_message("INFO", "Configuration loaded: ack_timeout = %d", config->ack_timeout);
        }
        else if (strcmp(key, "max_ack_size") == 0) {
            config->max_ack_size = atoi(value);
            log_message("INFO", "Configuration loaded: max_ack_size = %d", config->max_ack_size);
        }
        else if (strcmp(key, "max_buffer_size") == 0) {
            config->max_buffer_size = atoi(value);
            log_message("INFO", "Configuration loaded: max_buffer_size = %d", config->max_buffer_size);
        }
        else if (strcmp(key, "test_interval_hours") == 0) {
            config->test_interval_hours = atoi(value);
            log_message("INFO", "Configuration loaded: test_interval_hours = %d", config->test_interval_hours);
        }
        else if (strcmp(key, "health_check_enabled") == 0) {
            config->health_check_enabled = atoi(value);
            log_message("INFO", "Configuration loaded: health_check_enabled = %d", config->health_check_enabled);
        }
        else if (strcmp(key, "max_retry_attempts") == 0) {
            config->max_retry_attempts = atoi(value);
            log_message("INFO", "Configuration loaded: max_retry_attempts = %d", config->max_retry_attempts);
        }
        else if (strcmp(key, "retry_delay_seconds") == 0) {
            config->retry_delay_seconds = atoi(value);
            log_message("INFO", "Configuration loaded: retry_delay_seconds = %d", config->retry_delay_seconds);
        }
        else if (strcmp(key, "messages_directory") == 0) {
            // Validate the messages directory path for security
            if (strlen(value) >= sizeof(config->messages_directory)) {
                log_message("WARNING", "Messages directory path too long, using default");
                fprintf(stderr, "Warning: Messages directory path too long, using default\n");
            }
            else if (strstr(value, "..") != NULL) {
                log_message("WARNING", "Messages directory path contains directory traversal, using default");
                fprintf(stderr, "Warning: Messages directory path contains directory traversal, using default\n");
            }
            else {
                strncpy(config->messages_directory, value, sizeof(config->messages_directory) - 1);
                config->messages_directory[sizeof(config->messages_directory) - 1] = '\0';
                log_message("INFO", "Configuration loaded: messages_directory = %s", config->messages_directory);
            }
        }
        else if (strcmp(key, "alert_log_file") == 0) {
            // Validate the alert log file path for security
            if (strlen(value) >= sizeof(config->alert_log_file)) {
                log_message("WARNING", "Alert log file path too long, using default");
                fprintf(stderr, "Warning: Alert log file path too long, using default\n");
            }
            else if (strstr(value, "..") != NULL) {
                log_message("WARNING", "Alert log file path contains directory traversal, using default");
                fprintf(stderr, "Warning: Alert log file path contains directory traversal, using default\n");
            }
            else {
                strncpy(config->alert_log_file, value, sizeof(config->alert_log_file) - 1);
                config->alert_log_file[sizeof(config->alert_log_file) - 1] = '\0';
                log_message("INFO", "Configuration loaded: alert_log_file = %s", config->alert_log_file);
            }
        }
    }

    fclose(file);

    // Validate required configuration
    if (strlen(config->server_ip) == 0 || config->server_port == 0 ||
        config->ack_timeout == 0 || config->max_ack_size == 0 || config->max_buffer_size == 0) {
        log_message("ERROR", "Incomplete configuration found in file '%s'", filename);
        fprintf(stderr, "Error: Incomplete configuration found in file '%s'\n", filename);
        return -1;
    }

    // Validate ranges
    if (config->test_interval_hours < 1) config->test_interval_hours = 1;
    if (config->max_retry_attempts < 1) config->max_retry_attempts = 1;
    if (config->retry_delay_seconds < 1) config->retry_delay_seconds = 1;

    log_message("INFO", "Configuration successfully loaded from '%s'", filename);
    return 0;
}

// Function to handle errors
void error_exit(const char* message) {
    log_message("ERROR", "%s: %s", message, strerror(errno));
    fprintf(stderr, "Error: %s: %s\n", message, strerror(errno));
    close_logging();
    exit(1);
}

// Function to load test messages from directory
int load_test_messages(const char* directory, TestMessage* messages, int max_messages) {
    DIR* dir;
    struct dirent* entry;
    struct stat file_stat;
    int count = 0;
    char full_path[MAX_FILENAME_LENGTH];

    dir = opendir(directory);
    if (dir == NULL) {
        log_message("ERROR", "Cannot open messages directory '%s': %s", directory, strerror(errno));
        return -1;
    }

    log_message("INFO", "Loading test messages from directory: %s", directory);

    while ((entry = readdir(dir)) != NULL && count < max_messages) {
        // Skip directories and hidden files
        if (entry->d_name[0] == '.' || entry->d_type == DT_DIR) {
            continue;
        }

        // Skip files that don't end with .bin
        char* ext = strrchr(entry->d_name, '.');
        if (ext == NULL || strcmp(ext, ".bin") != 0) {
            continue;
        }

        // Build full path
        snprintf(full_path, sizeof(full_path), "%s/%s", directory, entry->d_name);

        // Get file stats
        if (stat(full_path, &file_stat) != 0) {
            log_message("WARNING", "Cannot stat file '%s': %s", full_path, strerror(errno));
            continue;
        }

        // Store message info
        strncpy(messages[count].filename, entry->d_name, sizeof(messages[count].filename) - 1);
        messages[count].filename[sizeof(messages[count].filename) - 1] = '\0';

        strncpy(messages[count].filepath, full_path, sizeof(messages[count].filepath) - 1);
        messages[count].filepath[sizeof(messages[count].filepath) - 1] = '\0';

        messages[count].size = file_stat.st_size;

        log_message("INFO", "Loaded test message: %s (%zu bytes)", messages[count].filename, messages[count].size);
        count++;
    }

    closedir(dir);
    log_message("INFO", "Loaded %d test messages from directory", count);
    return count;
}

// Function to analyze acknowledgment response
int analyze_ack_response(const char* ack_buffer, size_t ack_size) {
    // Basic HL7 ACK analysis
    if (ack_size == 0) {
        log_message("WARNING", "Empty acknowledgment received");
        return 0;
    }

    // Check for HL7 MSA segment (Message Acknowledgment)
    if (strstr(ack_buffer, "MSA") != NULL) {
        // Look for acknowledgment code
        if (strstr(ack_buffer, "MSA|AA") != NULL) {
            log_message("INFO", "Positive acknowledgment received (AA - Application Accept)");
            return 1;
        } else if (strstr(ack_buffer, "MSA|AE") != NULL) {
            log_message("WARNING", "Application error acknowledgment received (AE)");
            return 0;
        } else if (strstr(ack_buffer, "MSA|AR") != NULL) {
            log_message("WARNING", "Application reject acknowledgment received (AR)");
            return 0;
        }
    }

    // Check for simple positive responses
    if (strstr(ack_buffer, "OK") != NULL || strstr(ack_buffer, "ACK") != NULL) {
        log_message("INFO", "Simple positive acknowledgment received");
        return 1;
    }

    // Check for error indicators
    if (strstr(ack_buffer, "ERROR") != NULL || strstr(ack_buffer, "FAIL") != NULL) {
        log_message("WARNING", "Error indication in acknowledgment");
        return 0;
    }

    // Default: consider any non-empty response as positive
    log_message("INFO", "Non-standard acknowledgment received, considering as positive");
    return 1;
}

// Function to perform a single test with retry logic
int perform_single_test(const Config* config, const TestMessage* message) {
    int sockfd;
    struct sockaddr_in server_addr;
    char* buffer;
    char* ack_buffer;
    struct timeval timeout;
    FILE *file;
    ssize_t bytes_sent, bytes_received;
    int success = 0;
    int attempt;

    // Allocate buffers
    buffer = malloc(config->max_buffer_size);
    ack_buffer = malloc(config->max_ack_size);
    if (buffer == NULL || ack_buffer == NULL) {
        log_message("ERROR", "Memory allocation failed for test");
        if (buffer) free(buffer);
        if (ack_buffer) free(ack_buffer);
        return 0;
    }

    // Read message file
    file = fopen(message->filepath, "rb");
    if (file == NULL) {
        log_message("ERROR", "Cannot open test message file '%s': %s", message->filepath, strerror(errno));
        free(buffer);
        free(ack_buffer);
        return 0;
    }

    if (message->size > (size_t)config->max_buffer_size) {
        log_message("ERROR", "Test message too large (%zu bytes, max: %d bytes)", message->size, config->max_buffer_size);
        fclose(file);
        free(buffer);
        free(ack_buffer);
        return 0;
    }

    size_t bytes_read = fread(buffer, 1, message->size, file);
    fclose(file);

    if (bytes_read != message->size) {
        log_message("ERROR", "Error reading test message file: expected %zu bytes, read %zu bytes", message->size, bytes_read);
        free(buffer);
        free(ack_buffer);
        return 0;
    }

    // Retry logic
    for (attempt = 1; attempt <= config->max_retry_attempts && !success; attempt++) {
        log_message("INFO", "Test attempt %d/%d for message '%s'", attempt, config->max_retry_attempts, message->filename);

        // Create socket
        sockfd = socket(AF_INET, SOCK_STREAM, 0);
        if (sockfd < 0) {
            log_message("ERROR", "Error creating socket (attempt %d): %s", attempt, strerror(errno));
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        // Configure timeout
        timeout.tv_sec = config->ack_timeout;
        timeout.tv_usec = 0;
        if (setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
            log_message("ERROR", "Error setting socket timeout (attempt %d): %s", attempt, strerror(errno));
            close(sockfd);
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        // Configure server address
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(config->server_port);
        if (inet_pton(AF_INET, config->server_ip, &server_addr.sin_addr) <= 0) {
            log_message("ERROR", "Invalid server IP address: %s", config->server_ip);
            close(sockfd);
            break; // No point in retrying for invalid IP
        }

        // Connect to server
        if (connect(sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            log_message("ERROR", "Connection failed (attempt %d) to %s:%d: %s",
                       attempt, config->server_ip, config->server_port, strerror(errno));
            close(sockfd);
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        log_message("INFO", "Connected to %s:%d (attempt %d)", config->server_ip, config->server_port, attempt);

        // Send message
        bytes_sent = send(sockfd, buffer, message->size, 0);
        if (bytes_sent < 0) {
            log_message("ERROR", "Failed to send data (attempt %d): %s", attempt, strerror(errno));
            close(sockfd);
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        if ((size_t)bytes_sent != message->size) {
            log_message("ERROR", "Partial data sent (attempt %d): %zd bytes sent out of %zu bytes",
                       attempt, bytes_sent, message->size);
            close(sockfd);
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        log_message("INFO", "Data sent successfully (attempt %d): %zd bytes", attempt, bytes_sent);

        // Receive acknowledgment
        bytes_received = recv(sockfd, ack_buffer, config->max_ack_size - 1, 0);
        if (bytes_received < 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                log_message("ERROR", "Acknowledgment timeout (attempt %d) after %d seconds", attempt, config->ack_timeout);
            } else {
                log_message("ERROR", "Error receiving acknowledgment (attempt %d): %s", attempt, strerror(errno));
            }
            close(sockfd);
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        if (bytes_received == 0) {
            log_message("ERROR", "Connection closed by server without acknowledgment (attempt %d)", attempt);
            close(sockfd);
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        // Analyze acknowledgment
        ack_buffer[bytes_received] = '\0';
        log_message("INFO", "Acknowledgment received (attempt %d, %zd bytes): %s", attempt, bytes_received, ack_buffer);

        if (analyze_ack_response(ack_buffer, bytes_received)) {
            success = 1;
            log_message("INFO", "Test successful for message '%s' (attempt %d)", message->filename, attempt);
        } else {
            log_message("WARNING", "Test failed for message '%s' - negative acknowledgment (attempt %d)", message->filename, attempt);
        }

        close(sockfd);

        if (!success && attempt < config->max_retry_attempts) {
            sleep(config->retry_delay_seconds);
        }
    }

    free(buffer);
    free(ack_buffer);

    if (!success) {
        log_message("ERROR", "Test failed for message '%s' after %d attempts", message->filename, config->max_retry_attempts);
    }

    return success;
}

// Function to perform health check
int perform_health_check(const Config* config) {
    int sockfd;
    struct sockaddr_in server_addr;
    struct timeval timeout;

    // Create socket
    sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        log_message("WARNING", "Health check: Error creating socket: %s", strerror(errno));
        return 0;
    }

    // Set a shorter timeout for health checks
    timeout.tv_sec = 5;  // 5 seconds timeout for health checks
    timeout.tv_usec = 0;
    if (setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0 ||
        setsockopt(sockfd, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout)) < 0) {
        log_message("WARNING", "Health check: Error setting socket timeout: %s", strerror(errno));
        close(sockfd);
        return 0;
    }

    // Configure server address
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(config->server_port);
    if (inet_pton(AF_INET, config->server_ip, &server_addr.sin_addr) <= 0) {
        log_message("WARNING", "Health check: Invalid server IP address: %s", config->server_ip);
        close(sockfd);
        return 0;
    }

    // Try to connect
    if (connect(sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        log_message("WARNING", "Health check: Connection failed to %s:%d: %s",
                   config->server_ip, config->server_port, strerror(errno));
        close(sockfd);
        return 0;
    }

    log_message("INFO", "Health check: Server %s:%d is reachable", config->server_ip, config->server_port);
    close(sockfd);
    return 1;
}

// Function to update monitoring statistics
void update_stats(int test_result) {
    time_t now = time(NULL);

    stats.total_tests++;

    if (test_result) {
        stats.successful_tests++;
        stats.consecutive_failures = 0;
        stats.last_success = now;
    } else {
        stats.failed_tests++;
        stats.consecutive_failures++;
        stats.last_failure = now;

        // Generate alert if too many consecutive failures
        if (stats.consecutive_failures >= MAX_CONSECUTIVE_FAILURES) {
            log_alert("CRITICAL: %d consecutive test failures detected. Driver may be down!",
                     stats.consecutive_failures);
        }
    }
}

// Function to print monitoring statistics
void print_stats() {
    time_t now = time(NULL);
    double uptime = difftime(now, stats.start_time);
    double success_rate = (stats.total_tests > 0) ?
                         (double)stats.successful_tests / stats.total_tests * 100.0 : 0.0;

    printf("\n=== Monitoring Statistics ===\n");
    printf("Uptime: %.0f seconds (%.1f hours)\n", uptime, uptime / 3600.0);
    printf("Total tests: %d\n", stats.total_tests);
    printf("Successful tests: %d\n", stats.successful_tests);
    printf("Failed tests: %d\n", stats.failed_tests);
    printf("Success rate: %.1f%%\n", success_rate);
    printf("Consecutive failures: %d\n", stats.consecutive_failures);

    if (stats.last_success > 0) {
        printf("Last success: %s", ctime(&stats.last_success));
    } else {
        printf("Last success: Never\n");
    }

    if (stats.last_failure > 0) {
        printf("Last failure: %s", ctime(&stats.last_failure));
    } else {
        printf("Last failure: Never\n");
    }
    printf("=============================\n\n");

    log_message("INFO", "Statistics - Total: %d, Success: %d, Failed: %d, Success rate: %.1f%%, Consecutive failures: %d",
               stats.total_tests, stats.successful_tests, stats.failed_tests, success_rate, stats.consecutive_failures);
}

int main(int argc, char *argv[]) {
    Config config;
    TestMessage messages[MAX_MESSAGES];
    int message_count = 0;
    const char* config_file = CONFIG_FILE;
    time_t last_test_time = 0;
    time_t last_health_check = 0;
    time_t now;
    int test_interval_seconds;
    int i, test_result;
    int daemon_mode = 0;

    // Setup signal handlers
    setup_signal_handlers();

    // Check arguments
    if (argc < 1 || argc > 3) {
        fprintf(stderr, "Usage: %s [--daemon] [config_file]\n", argv[0]);
        fprintf(stderr, "  --daemon: Run in continuous monitoring mode\n");
        fprintf(stderr, "  config_file: Alternative configuration file (default: %s)\n", CONFIG_FILE);
        exit(1);
    }

    // Parse arguments
    for (i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--daemon") == 0) {
            daemon_mode = 1;
        } else {
            config_file = argv[i];
        }
    }

    // Load configuration first to get alert log path
    if (load_config(&config, config_file) != 0) {
        exit(1);
    }

    // Initialize logging system with alert log
    if (init_logging(config.alert_log_file) != 0) {
        fprintf(stderr, "Error: Failed to initialize logging system\n");
        exit(1);
    }

    log_message("INFO", "Automated Driver Tester started (daemon mode: %s)", daemon_mode ? "enabled" : "disabled");

    // Initialize statistics
    stats.start_time = time(NULL);

    // Display configuration
    printf("=== Configuration ===\n");
    printf("Server IP: %s\n", config.server_ip);
    printf("Server Port: %d\n", config.server_port);
    printf("ACK Timeout: %d seconds\n", config.ack_timeout);
    printf("Max ACK Size: %d bytes\n", config.max_ack_size);
    printf("Max Buffer Size: %d bytes\n", config.max_buffer_size);
    printf("Test Interval: %d hours\n", config.test_interval_hours);
    printf("Health Check: %s\n", config.health_check_enabled ? "enabled" : "disabled");
    printf("Max Retry Attempts: %d\n", config.max_retry_attempts);
    printf("Retry Delay: %d seconds\n", config.retry_delay_seconds);
    printf("Messages Directory: %s\n", config.messages_directory);
    printf("Alert Log File: %s\n", config.alert_log_file);
    printf("Daemon Mode: %s\n", daemon_mode ? "enabled" : "disabled");
    printf("====================\n\n");

    // Load test messages
    message_count = load_test_messages(config.messages_directory, messages, MAX_MESSAGES);
    if (message_count <= 0) {
        log_message("ERROR", "No test messages found in directory '%s'", config.messages_directory);
        fprintf(stderr, "Error: No test messages found in directory '%s'\n", config.messages_directory);
        close_logging();
        exit(1);
    }

    printf("Loaded %d test messages\n", message_count);
    log_message("INFO", "Loaded %d test messages for testing", message_count);

    // Calculate test interval in seconds
    test_interval_seconds = config.test_interval_hours * 3600;

    if (!daemon_mode) {
        // Single test mode - run all messages once
        printf("Running single test cycle...\n");
        log_message("INFO", "Running single test cycle with %d messages", message_count);

        int overall_success = 1;
        for (i = 0; i < message_count && keep_running; i++) {
            printf("Testing message %d/%d: %s\n", i + 1, message_count, messages[i].filename);
            test_result = perform_single_test(&config, &messages[i]);
            update_stats(test_result);

            if (!test_result) {
                overall_success = 0;
            }

            if (i < message_count - 1) {
                printf("Waiting 2 seconds before next test...\n");
                sleep(2);
            }
        }

        print_stats();
        log_message("INFO", "Single test cycle completed");

        if (!overall_success) {
            log_message("ERROR", "One or more tests failed in single test mode");
            close_logging();
            return 1;
        }
    } else {
        // Daemon mode - continuous monitoring
        printf("Starting continuous monitoring mode...\n");
        printf("Test interval: %d hours (%d seconds)\n", config.test_interval_hours, test_interval_seconds);
        printf("Health check: %s\n", config.health_check_enabled ? "enabled" : "disabled");
        printf("Press Ctrl+C to stop gracefully\n\n");

        log_message("INFO", "Starting continuous monitoring mode");
        log_message("INFO", "Test interval: %d hours, Health check: %s",
                   config.test_interval_hours, config.health_check_enabled ? "enabled" : "disabled");

        while (keep_running) {
            now = time(NULL);

            // Check if configuration should be reloaded
            if (reload_config) {
                log_message("INFO", "Reloading configuration...");
                if (load_config(&config, config_file) == 0) {
                    test_interval_seconds = config.test_interval_hours * 3600;
                    log_message("INFO", "Configuration reloaded successfully");
                    printf("Configuration reloaded\n");
                } else {
                    log_message("ERROR", "Failed to reload configuration, keeping current settings");
                }
                reload_config = 0;
            }

            // Perform scheduled tests
            if (now - last_test_time >= test_interval_seconds) {
                printf("\n=== Starting scheduled test cycle ===\n");
                log_message("INFO", "Starting scheduled test cycle");

                for (i = 0; i < message_count && keep_running; i++) {
                    printf("Testing message %d/%d: %s\n", i + 1, message_count, messages[i].filename);
                    test_result = perform_single_test(&config, &messages[i]);
                    update_stats(test_result);

                    // Small delay between tests to avoid overwhelming the server
                    if (i < message_count - 1 && keep_running) {
                        sleep(2);
                    }
                }

                last_test_time = now;
                print_stats();
                printf("=== Test cycle completed ===\n\n");
                log_message("INFO", "Scheduled test cycle completed");
            }

            // Perform health checks
            if (config.health_check_enabled && (now - last_health_check >= HEALTH_CHECK_INTERVAL)) {
                if (perform_health_check(&config)) {
                    log_message("INFO", "Health check passed");
                } else {
                    log_message("WARNING", "Health check failed - server may be unreachable");
                }
                last_health_check = now;
            }

            // Sleep for a short interval before checking again
            sleep(1);
        }

        printf("\nShutdown signal received, stopping gracefully...\n");
        log_message("INFO", "Shutdown signal received, stopping gracefully");
        print_stats();
    }

    log_message("INFO", "Automated Driver Tester shutting down");
    close_logging();
    return 0;
}