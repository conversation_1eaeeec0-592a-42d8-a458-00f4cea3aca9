#!/bin/bash

# Script de démarrage Podman pour le testeur automatisé d'automate de biologie médicale
# Optimisé pour Red Hat Enterprise Linux et cartes iMX8 mini

set -e

# Configuration par défaut
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/server_tester.ini"
IMAGE_NAME="automate_tester"
CONTAINER_NAME="automate_tester_service"
LOG_DIR="/var/log/automate_tester"
DATA_DIR="/opt/shared-data"

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction d'aide
show_help() {
    echo "Testeur automatisé pour driver universel d'automate de biologie médicale"
    echo "Version Red Hat / Podman pour iMX8 mini"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -b, --build           Construire l'image Podman"
    echo "  -r, --run             Lancer le conteneur"
    echo "  -s, --stop            Arrêter le conteneur"
    echo "  -l, --logs            Afficher les logs"
    echo "  -t, --status          Afficher le statut"
    echo "  --clean               Nettoyer les conteneurs et images"
    echo "  --setup               Configuration initiale du système"
    echo "  -h, --help            Afficher cette aide"
    echo ""
    echo "Exemples:"
    echo "  $0 --setup           # Configuration initiale"
    echo "  $0 --build           # Construire l'image"
    echo "  $0 --run             # Lancer le service"
    echo "  $0 --logs            # Voir les logs"
    echo "  $0 --stop            # Arrêter le service"
}

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

# Vérifier que Podman est installé
check_podman() {
    if ! command -v podman &> /dev/null; then
        log_error "Podman n'est pas installé"
        echo "Installation sur Red Hat/CentOS:"
        echo "  sudo dnf install -y podman podman-compose"
        exit 1
    fi
    
    log_success "Podman disponible: $(podman --version)"
}

# Configuration initiale du système
setup_system() {
    log "=== Configuration initiale du système ==="
    
    # Vérifier les privilèges
    if [[ $EUID -ne 0 ]]; then
        log_error "La configuration initiale nécessite les privilèges root"
        echo "Utilisez: sudo $0 --setup"
        exit 1
    fi
    
    # Créer les répertoires système
    log "Création des répertoires système..."
    mkdir -p "$LOG_DIR"
    mkdir -p "$DATA_DIR"
    
    # Configurer les permissions et SELinux
    log "Configuration des permissions et SELinux..."
    chown -R 1001:1001 "$LOG_DIR"
    chown -R 1001:1001 "$DATA_DIR"
    chmod 755 "$LOG_DIR"
    chmod 755 "$DATA_DIR"
    
    # Configuration SELinux pour les volumes
    if command -v setsebool &> /dev/null; then
        log "Configuration SELinux..."
        setsebool -P container_manage_cgroup on
        semanage fcontext -a -t container_file_t "$LOG_DIR(/.*)?" 2>/dev/null || true
        semanage fcontext -a -t container_file_t "$DATA_DIR(/.*)?" 2>/dev/null || true
        restorecon -R "$LOG_DIR" 2>/dev/null || true
        restorecon -R "$DATA_DIR" 2>/dev/null || true
    fi
    
    # Configurer le service systemd pour Podman
    log "Configuration du service systemd..."
    cat > /etc/systemd/system/automate-tester-podman.service << EOF
[Unit]
Description=Automate Tester Podman Service
Wants=network-online.target
After=network-online.target
RequiresMountsFor=%t/containers

[Service]
Environment=PODMAN_SYSTEMD_UNIT=%n
Restart=on-failure
TimeoutStopSec=70
ExecStartPre=/bin/rm -f %t/%n.ctr-id
ExecStart=/usr/bin/podman run \\
    --cidfile=%t/%n.ctr-id \\
    --cgroups=no-conmon \\
    --rm \\
    --sdnotify=conmon \\
    --replace \\
    --name $CONTAINER_NAME \\
    --network host \\
    -v $LOG_DIR:/opt/automate_tester/var/logs:Z \\
    -v $DATA_DIR:/opt/automate_tester/var/data:Z \\
    -v $SCRIPT_DIR/server_tester.ini:/opt/automate_tester/etc/config.ini:ro,Z \\
    $IMAGE_NAME:latest
ExecStop=/usr/bin/podman stop --ignore --cidfile=%t/%n.ctr-id
ExecStopPost=/usr/bin/podman rm -f --ignore --cidfile=%t/%n.ctr-id
Type=notify
NotifyAccess=all

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    
    log_success "Configuration système terminée"
    echo ""
    echo "Prochaines étapes:"
    echo "1. Construire l'image: $0 --build"
    echo "2. Lancer le service: systemctl start automate-tester-podman"
    echo "3. Activer au démarrage: systemctl enable automate-tester-podman"
}

# Construire l'image
build_image() {
    log "=== Construction de l'image Podman ==="
    
    check_podman
    
    # Vérifier les fichiers nécessaires
    if [[ ! -f "$SCRIPT_DIR/Dockerfile" ]]; then
        log_error "Dockerfile non trouvé dans $SCRIPT_DIR"
        exit 1
    fi
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "Fichier de configuration non trouvé: $CONFIG_FILE"
        exit 1
    fi
    
    # Construire l'image
    log "Construction de l'image $IMAGE_NAME..."
    cd "$SCRIPT_DIR"
    
    podman build \
        --tag "$IMAGE_NAME:latest" \
        --file Dockerfile \
        --format docker \
        .
    
    if [[ $? -eq 0 ]]; then
        log_success "Image construite avec succès"
        podman images | grep "$IMAGE_NAME"
    else
        log_error "Échec de la construction de l'image"
        exit 1
    fi
}

# Lancer le conteneur
run_container() {
    log "=== Lancement du conteneur ==="
    
    check_podman
    
    # Vérifier que l'image existe
    if ! podman image exists "$IMAGE_NAME:latest"; then
        log_warning "Image non trouvée, construction en cours..."
        build_image
    fi
    
    # Arrêter le conteneur existant s'il existe
    if podman container exists "$CONTAINER_NAME"; then
        log "Arrêt du conteneur existant..."
        podman stop "$CONTAINER_NAME" || true
        podman rm "$CONTAINER_NAME" || true
    fi
    
    # Créer les répertoires s'ils n'existent pas
    sudo mkdir -p "$LOG_DIR" "$DATA_DIR"
    
    # Lancer le nouveau conteneur
    log "Lancement du conteneur $CONTAINER_NAME..."
    
    podman run -d \
        --name "$CONTAINER_NAME" \
        --network host \
        --restart unless-stopped \
        -v "$LOG_DIR:/opt/automate_tester/var/logs:Z" \
        -v "$DATA_DIR:/opt/automate_tester/var/data:Z" \
        -v "$CONFIG_FILE:/opt/automate_tester/etc/config.ini:ro,Z" \
        -e TZ=Europe/Paris \
        "$IMAGE_NAME:latest"
    
    if [[ $? -eq 0 ]]; then
        log_success "Conteneur lancé avec succès"
        sleep 2
        show_status
    else
        log_error "Échec du lancement du conteneur"
        exit 1
    fi
}

# Arrêter le conteneur
stop_container() {
    log "=== Arrêt du conteneur ==="
    
    check_podman
    
    if podman container exists "$CONTAINER_NAME"; then
        log "Arrêt du conteneur $CONTAINER_NAME..."
        podman stop "$CONTAINER_NAME"
        podman rm "$CONTAINER_NAME"
        log_success "Conteneur arrêté"
    else
        log_warning "Aucun conteneur $CONTAINER_NAME en cours d'exécution"
    fi
}

# Afficher les logs
show_logs() {
    log "=== Logs du conteneur ==="
    
    check_podman
    
    if podman container exists "$CONTAINER_NAME"; then
        podman logs -f "$CONTAINER_NAME"
    else
        log_error "Conteneur $CONTAINER_NAME non trouvé"
        exit 1
    fi
}

# Afficher le statut
show_status() {
    log "=== Statut du service ==="
    
    check_podman
    
    # Statut du conteneur
    if podman container exists "$CONTAINER_NAME"; then
        local status=$(podman inspect "$CONTAINER_NAME" --format '{{.State.Status}}')
        if [[ "$status" == "running" ]]; then
            log_success "Conteneur en cours d'exécution"
            echo "Détails du conteneur:"
            podman ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        else
            log_warning "Conteneur existe mais n'est pas en cours d'exécution (statut: $status)"
        fi
    else
        log "Aucun conteneur $CONTAINER_NAME trouvé"
    fi
    
    # Statut du service systemd
    if systemctl is-active --quiet automate-tester-podman 2>/dev/null; then
        log_success "Service systemd actif"
    else
        log "Service systemd non actif"
    fi
    
    # Afficher les derniers logs
    if [[ -d "$LOG_DIR" ]]; then
        local latest_log=$(find "$LOG_DIR" -name "*.log" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)
        if [[ -n "$latest_log" ]]; then
            echo ""
            echo "Dernières entrées de log:"
            tail -5 "$latest_log" 2>/dev/null | sed 's/^/  /' || echo "  Aucun log disponible"
        fi
    fi
}

# Nettoyer les conteneurs et images
clean_all() {
    log "=== Nettoyage ==="
    
    check_podman
    
    # Arrêter et supprimer le conteneur
    if podman container exists "$CONTAINER_NAME"; then
        log "Suppression du conteneur $CONTAINER_NAME..."
        podman stop "$CONTAINER_NAME" || true
        podman rm "$CONTAINER_NAME" || true
    fi
    
    # Supprimer l'image
    if podman image exists "$IMAGE_NAME:latest"; then
        log "Suppression de l'image $IMAGE_NAME..."
        podman rmi "$IMAGE_NAME:latest" || true
    fi
    
    # Nettoyer les images orphelines
    log "Nettoyage des images orphelines..."
    podman image prune -f
    
    log_success "Nettoyage terminé"
}

# Traitement des arguments
case "${1:-}" in
    -b|--build)
        build_image
        ;;
    -r|--run)
        run_container
        ;;
    -s|--stop)
        stop_container
        ;;
    -l|--logs)
        show_logs
        ;;
    -t|--status)
        show_status
        ;;
    --clean)
        clean_all
        ;;
    --setup)
        setup_system
        ;;
    -h|--help)
        show_help
        ;;
    *)
        echo "Usage: $0 {--build|--run|--stop|--logs|--status|--clean|--setup|--help}"
        echo "Utilisez '$0 --help' pour plus d'informations"
        exit 1
        ;;
esac
