# Configuration de test pour démontrer la sécurité contre Path Traversal
# Ce fichier contient des tentatives d'attaque par traversée de répertoire

server_ip = 127.0.0.1
server_port = 8080
ack_timeout = 10
max_ack_size = 1024
max_buffer_size = 4096

# Test d'attaque par traversée de répertoire - ces chemins seront bloqués
alert_log_file = ../../../etc/passwd
messages_directory = ../../sensitive_data

# Configuration normale
test_interval_hours = 1
health_check_enabled = 1
max_retry_attempts = 3
retry_delay_seconds = 5
