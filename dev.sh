#!/bin/bash

# Development helper script for Automated Driver Tester
# Usage: ./dev.sh [command]

set -e

PROJECT_NAME="Automated Driver Tester"
VERSION="2.0.0"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Commands
cmd_build() {
    log_info "Building $PROJECT_NAME v$VERSION..."
    
    if [ "$1" = "debug" ]; then
        log_info "Building debug version..."
        make debug
    elif [ "$1" = "cmake" ]; then
        log_info "Building with CMake..."
        make cmake
    else
        log_info "Building release version..."
        make release
    fi
    
    log_success "Build completed!"
}

cmd_clean() {
    log_info "Cleaning build files..."
    make clean
    log_success "Clean completed!"
}

cmd_test() {
    log_info "Running tests..."
    
    # Build first
    make release > /dev/null 2>&1
    
    # Run basic functionality test
    log_info "Testing basic functionality..."
    timeout 3s ./build/bin/server_tester > /dev/null 2>&1 || true
    
    # Check if executable exists and is working
    if [ -f "./build/bin/server_tester" ]; then
        log_success "Executable created successfully"
    else
        log_error "Executable not found"
        return 1
    fi
    
    log_success "Tests completed!"
}

cmd_format() {
    log_info "Formatting code..."
    
    # Check if clang-format is available
    if command -v clang-format >/dev/null 2>&1; then
        find src include -name "*.c" -o -name "*.h" | xargs clang-format -i
        log_success "Code formatted!"
    else
        log_warning "clang-format not found, skipping formatting"
    fi
}

cmd_analyze() {
    log_info "Running static analysis..."
    
    # Check if cppcheck is available
    if command -v cppcheck >/dev/null 2>&1; then
        cppcheck --enable=all --inconclusive --std=c11 -I include src/
        log_success "Static analysis completed!"
    else
        log_warning "cppcheck not found, skipping static analysis"
    fi
}

cmd_install() {
    log_info "Installing $PROJECT_NAME..."
    make install
    log_success "Installation completed!"
}

cmd_dev_setup() {
    log_info "Setting up development environment..."
    
    # Create necessary directories
    mkdir -p build/bin build/obj
    
    # Check for required tools
    tools=("gcc" "make" "cmake")
    missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -eq 0 ]; then
        log_success "All required tools are available"
    else
        log_warning "Missing tools: ${missing_tools[*]}"
        log_info "Please install missing tools before continuing"
    fi
    
    # Optional tools
    optional_tools=("clang-format" "cppcheck" "valgrind")
    available_optional=()
    
    for tool in "${optional_tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            available_optional+=("$tool")
        fi
    done
    
    if [ ${#available_optional[@]} -gt 0 ]; then
        log_info "Available optional tools: ${available_optional[*]}"
    fi
    
    log_success "Development environment setup completed!"
}

cmd_info() {
    echo "=== $PROJECT_NAME v$VERSION ==="
    echo "Build system: Make + CMake"
    echo "Language: C11"
    echo "Architecture: Modular"
    echo
    echo "Directory structure:"
    echo "  src/      - Source files"
    echo "  include/  - Header files"
    echo "  build/    - Build artifacts"
    echo "  logs/     - Application logs"
    echo "  messages/ - Test messages"
    echo
    make info
}

cmd_help() {
    echo "Development helper script for $PROJECT_NAME"
    echo
    echo "Usage: $0 [command]"
    echo
    echo "Commands:"
    echo "  build [debug|cmake]  - Build the project"
    echo "  clean               - Clean build files"
    echo "  test                - Run tests"
    echo "  format              - Format code (requires clang-format)"
    echo "  analyze             - Run static analysis (requires cppcheck)"
    echo "  install             - Install the application"
    echo "  dev-setup           - Setup development environment"
    echo "  info                - Show project information"
    echo "  help                - Show this help"
    echo
    echo "Examples:"
    echo "  $0 build debug      - Build debug version"
    echo "  $0 test             - Run all tests"
    echo "  $0 format           - Format all source code"
}

# Main script logic
case "${1:-help}" in
    build)
        cmd_build "$2"
        ;;
    clean)
        cmd_clean
        ;;
    test)
        cmd_test
        ;;
    format)
        cmd_format
        ;;
    analyze)
        cmd_analyze
        ;;
    install)
        cmd_install
        ;;
    dev-setup)
        cmd_dev_setup
        ;;
    info)
        cmd_info
        ;;
    help|--help|-h)
        cmd_help
        ;;
    *)
        log_error "Unknown command: $1"
        echo
        cmd_help
        exit 1
        ;;
esac
