# Simple Makefile for quick compilation
# For full CMake build, use: cd build && make

CC = gcc
CFLAGS = -Wall -Wextra -std=c11 -Iutils
SRCDIR = .
UTILSDIR = utils
BUILDDIR = build

# Source files
SOURCES = main.c \
          $(UTILSDIR)/utils.c \
          $(UTILSDIR)/config.c \
          $(UTILSDIR)/logging.c \
          $(UTILSDIR)/network.c \
          $(UTILSDIR)/monitoring.c

# Object files
OBJECTS = $(SOURCES:.c=.o)

# Target executable
TARGET = server_tester

.PHONY: all clean cmake install

# Default target
all: $(TARGET)

# Build executable
$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $(TARGET)

# Compile source files
%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

# Clean build files
clean:
	rm -f $(OBJECTS) $(TARGET)
	rm -rf $(BUILDDIR)/*

# Use CMake build (recommended)
cmake:
	mkdir -p $(BUILDDIR)
	cd $(BUILDDIR) && cmake .. && make

# Install (copy to /usr/local/bin)
install: cmake
	sudo cp $(BUILDDIR)/PPAEMD_Automates_simul /usr/local/bin/server_tester
	sudo chmod +x /usr/local/bin/server_tester

# Help
help:
	@echo "Available targets:"
	@echo "  all     - Build with simple Makefile"
	@echo "  cmake   - Build with CMake (recommended)"
	@echo "  clean   - Clean build files"
	@echo "  install - Install to /usr/local/bin"
	@echo "  help    - Show this help"
