#!/bin/bash

# Script de test pour vérifier la refactorisation

echo "=== Test de la Refactorisation ==="
echo

# Test 1: Vérification de la structure
echo "1. Vérification de la structure des fichiers..."
if [ -d "src" ] && [ -d "include" ] && [ -f "src/main.c" ] && [ -f "include/common.h" ]; then
    echo "✅ Structure v2.0 correcte (src/ + include/)"
else
    echo "❌ Structure incorrecte"
    exit 1
fi

# Test 2: Compilation avec CMake
echo
echo "2. Test de compilation avec CMake..."
if make cmake > /dev/null 2>&1; then
    echo "✅ Compilation CMake réussie"
else
    echo "❌ Compilation CMake échouée"
    exit 1
fi

# Test 3: Compilation avec Makefile simple
echo
echo "3. Test de compilation avec Makefile simple..."
# Sauvegarder l'exécutable CMake s'il existe
if [ -f "build/PPAEMD_Automates_simul" ]; then
    cp build/PPAEMD_Automates_simul build/PPAEMD_Automates_simul.backup
fi

if make clean > /dev/null 2>&1 && make > /dev/null 2>&1; then
    echo "✅ Compilation Makefile réussie"
else
    echo "❌ Compilation Makefile échouée"
    exit 1
fi

# Restaurer l'exécutable CMake
if [ -f "build/PPAEMD_Automates_simul.backup" ]; then
    mkdir -p build
    mv build/PPAEMD_Automates_simul.backup build/PPAEMD_Automates_simul
fi

# Test 4: Vérification des exécutables
echo
echo "4. Vérification des exécutables..."
cmake_exe_exists=false
makefile_exe_exists=false

if [ -f "build/bin/PPAEMD_Automates_simul" ]; then
    cmake_exe_exists=true
    echo "  ✅ Exécutable CMake: build/bin/PPAEMD_Automates_simul"
fi

if [ -f "build/bin/server_tester" ]; then
    makefile_exe_exists=true
    echo "  ✅ Exécutable Makefile: build/bin/server_tester"
fi

if [ "$cmake_exe_exists" = true ] || [ "$makefile_exe_exists" = true ]; then
    echo "✅ Au moins un exécutable créé"
else
    echo "❌ Aucun exécutable trouvé"
    exit 1
fi

# Test 5: Test de fonctionnement (version courte)
echo
echo "5. Test de fonctionnement..."
if [ -f "build/bin/server_tester" ]; then
    timeout 3s ./build/bin/server_tester > /dev/null 2>&1
    if [ $? -eq 124 ]; then  # timeout = programme a démarré
        echo "✅ Programme fonctionne"
    else
        echo "⚠️  Programme a terminé rapidement (normal si pas de serveur)"
    fi
else
    echo "⚠️  Exécutable non trouvé, test ignoré"
fi

# Test 6: Vérification des modules
echo
echo "6. Vérification des modules..."
modules=("utils.c" "config.c" "logging.c" "network.c" "monitoring.c" "version.c")
headers=("utils.h" "config.h" "logging.h" "network.h" "monitoring.h" "common.h")
all_modules_exist=true

for module in "${modules[@]}"; do
    if [ -f "src/$module" ]; then
        echo "  ✅ src/$module"
    else
        echo "  ❌ src/$module manquant"
        all_modules_exist=false
    fi
done

for header in "${headers[@]}"; do
    if [ -f "include/$header" ]; then
        echo "  ✅ include/$header"
    else
        echo "  ❌ include/$header manquant"
        all_modules_exist=false
    fi
done

if [ "$all_modules_exist" = true ]; then
    echo "✅ Tous les modules présents"
else
    echo "❌ Modules manquants"
    exit 1
fi

# Test 7: Vérification que l'ancien fichier a été supprimé
echo
echo "7. Vérification de la suppression de l'ancien fichier..."
if [ ! -f "server_tester.c" ]; then
    echo "✅ Ancien fichier server_tester.c supprimé"
else
    echo "❌ Ancien fichier server_tester.c encore présent"
fi

echo
echo "=== Résumé ==="
echo "✅ Refactorisation v2.0 réussie !"
echo "✅ Structure professionnelle (src/ + include/)"
echo "✅ Code modulaire et maintenable"
echo "✅ Système de versioning intégré"
echo "✅ Script de développement (dev.sh)"
echo "✅ Deux méthodes de compilation disponibles"
echo "✅ Fonctionnalités préservées"
echo
echo "Utilisation:"
echo "  - Script dev: ./dev.sh build"
echo "  - Compilation CMake: make cmake"
echo "  - Compilation Make: make release"
echo "  - Exécution: ./build/bin/server_tester"
echo "  - Tests: ./dev.sh test"
echo
