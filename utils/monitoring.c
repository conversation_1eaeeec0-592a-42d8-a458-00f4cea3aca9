#include "monitoring.h"
#include "logging.h"

// Global monitoring statistics
MonitoringStats stats = {0};

// Function to update monitoring statistics
void update_stats(int test_result) {
    time_t now = time(NULL);

    stats.total_tests++;

    if (test_result) {
        stats.successful_tests++;
        stats.consecutive_failures = 0;
        stats.last_success = now;
    } else {
        stats.failed_tests++;
        stats.consecutive_failures++;
        stats.last_failure = now;

        // Generate alert if too many consecutive failures
        if (stats.consecutive_failures >= MAX_CONSECUTIVE_FAILURES) {
            log_alert("CRITICAL: %d consecutive test failures detected. Driver may be down!",
                     stats.consecutive_failures);
        }
    }
}

// Function to print monitoring statistics
void print_stats(void) {
    time_t now = time(NULL);
    double uptime = difftime(now, stats.start_time);
    double success_rate = (stats.total_tests > 0) ?
                         (double)stats.successful_tests / stats.total_tests * 100.0 : 0.0;

    printf("\n=== Monitoring Statistics ===\n");
    printf("Uptime: %.0f seconds (%.1f hours)\n", uptime, uptime / 3600.0);
    printf("Total tests: %d\n", stats.total_tests);
    printf("Successful tests: %d\n", stats.successful_tests);
    printf("Failed tests: %d\n", stats.failed_tests);
    printf("Success rate: %.1f%%\n", success_rate);
    printf("Consecutive failures: %d\n", stats.consecutive_failures);

    if (stats.last_success > 0) {
        printf("Last success: %s", ctime(&stats.last_success));
    } else {
        printf("Last success: Never\n");
    }

    if (stats.last_failure > 0) {
        printf("Last failure: %s", ctime(&stats.last_failure));
    } else {
        printf("Last failure: Never\n");
    }
    printf("=============================\n\n");

    log_message("INFO", "Statistics - Total: %d, Success: %d, Failed: %d, Success rate: %.1f%%, Consecutive failures: %d",
               stats.total_tests, stats.successful_tests, stats.failed_tests, success_rate, stats.consecutive_failures);
}
