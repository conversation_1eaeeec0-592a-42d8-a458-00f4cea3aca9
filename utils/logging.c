#include "logging.h"
#include "utils.h"

// Global log file pointers
FILE* log_file = NULL;
FILE* alert_log_file = NULL;

// Initialize logging system
int init_logging(const char* alert_log_path) {
    time_t now;
    struct tm* tm_info;
    char log_filename[MAX_LOG_PATH];
    char timestamp[20];
    char current_dir[PATH_MAX];

    // Get current working directory for path validation
    if (getcwd(current_dir, sizeof(current_dir)) == NULL) {
        fprintf(stderr, "Error: Failed to get current directory: %s\n", strerror(errno));
        return -1;
    }

    // Create a logs directory if it doesn't exist
    struct stat st = {0};
    if (stat(LOG_DIR, &st) == -1) {
        if (mkdir(LOG_DIR, 0755) != 0) {
            fprintf(stderr, "Error: Failed to create logs directory: %s\n", strerror(errno));
            return -1;
        }
    }

    // Get current date for log filename
    time(&now);
    tm_info = localtime(&now);
    strftime(timestamp, sizeof(timestamp), "%Y%m%d", tm_info);

    // Create log filename
    snprintf(log_filename, sizeof(log_filename), "%s/server_tester_%s.log", LOG_DIR, timestamp);

    // Open log file in append mode
    log_file = fopen(log_filename, "a");
    if (log_file == NULL) {
        fprintf(stderr, "Error: Failed to open log file '%s': %s\n", log_filename, strerror(errno));
        return -1;
    }

    // Open alert log file if specified with security validation
    if (alert_log_path && strlen(alert_log_path) > 0) {
        // Sanitize the alert log path to prevent path traversal
        char* sanitized_alert_path = sanitize_path(alert_log_path);

        if (sanitized_alert_path == NULL) {
            fprintf(stderr, "Warning: Invalid alert log path '%s', skipping alert logging\n", alert_log_path);
            return 0;
        }

        // Additional validation: ensure the path doesn't go outside reasonable bounds
        // Allow paths starting with '/' (absolute) or relative paths within current directory
        if (strstr(sanitized_alert_path, "..") != NULL) {
            fprintf(stderr, "Warning: Alert log path '%s' contains directory traversal, skipping alert logging\n", alert_log_path);
            return 0;
        }

        // Create directory for alert log if it doesn't exist
        char alert_path_copy[PATH_MAX];
        strncpy(alert_path_copy, sanitized_alert_path, sizeof(alert_path_copy) - 1);
        alert_path_copy[sizeof(alert_path_copy) - 1] = '\0';

        char* last_slash = strrchr(alert_path_copy, '/');
        if (last_slash != NULL) {
            *last_slash = '\0'; // Truncate to get directory path

            if (strlen(alert_path_copy) > 0 && strcmp(alert_path_copy, ".") != 0) {
                struct stat alert_st = {0};
                if (stat(alert_path_copy, &alert_st) == -1) {
                    if (mkdir(alert_path_copy, 0755) != 0) {
                        fprintf(stderr, "Warning: Failed to create alert log directory '%s': %s\n", alert_path_copy, strerror(errno));
                        // Continue without alert logging
                        return 0;
                    }
                }
            }
        }

        alert_log_file = fopen(sanitized_alert_path, "a");
        if (alert_log_file == NULL) {
            fprintf(stderr, "Warning: Failed to open alert log file '%s': %s\n", sanitized_alert_path, strerror(errno));
            // Continue without alert logging
        }
    }

    return 0;
}

// Log a message with timestamp
void log_message(const char* level, const char* format, ...) {
    if (log_file == NULL) return;

    time_t now;
    struct tm* tm_info;
    char timestamp[32];
    va_list args;

    // Get current timestamp
    time(&now);
    tm_info = localtime(&now);
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);

    // Write timestamp and level to log
    fprintf(log_file, "[%s] [%s] ", timestamp, level);

    // Write formatted message
    va_start(args, format);
    vfprintf(log_file, format, args);
    va_end(args);

    fprintf(log_file, "\n");
    fflush(log_file);
}

// Log an alert message (both to regular log and alert log)
void log_alert(const char* format, ...) {
    time_t now;
    struct tm* tm_info;
    char timestamp[32];
    va_list args;

    // Get current timestamp
    time(&now);
    tm_info = localtime(&now);
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);

    // Log to regular log
    if (log_file != NULL) {
        fprintf(log_file, "[%s] [ALERT] ", timestamp);
        va_start(args, format);
        vfprintf(log_file, format, args);
        va_end(args);
        fprintf(log_file, "\n");
        fflush(log_file);
    }

    // Log to alert log
    if (alert_log_file != NULL) {
        fprintf(alert_log_file, "[%s] [ALERT] ", timestamp);
        va_start(args, format);
        vfprintf(alert_log_file, format, args);
        va_end(args);
        fprintf(alert_log_file, "\n");
        fflush(alert_log_file);
    }

    // Also print to stderr for immediate visibility
    fprintf(stderr, "[%s] [ALERT] ", timestamp);
    va_start(args, format);
    vfprintf(stderr, format, args);
    va_end(args);
    fprintf(stderr, "\n");
}

// Close logging system
void close_logging(void) {
    if (log_file != NULL) {
        fclose(log_file);
        log_file = NULL;
    }
    if (alert_log_file != NULL) {
        fclose(alert_log_file);
        alert_log_file = NULL;
    }
}
