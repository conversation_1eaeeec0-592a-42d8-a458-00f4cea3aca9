#include "config.h"
#include "logging.h"
#include "utils.h"

// Function to load configuration from INI file
int load_config(Config* config, const char* filename) {
    FILE* file;
    char line[MAX_LINE_LENGTH];
    char* key;
    char* value;
    char* equals_pos;

    // Initialize config structure with default values
    memset(config, 0, sizeof(Config));
    config->test_interval_hours = 1;  // Default: test every hour
    config->health_check_enabled = 1; // Default: health check enabled
    config->max_retry_attempts = 3;   // Default: 3 retry attempts
    config->retry_delay_seconds = 5;  // Default: 5 seconds between retries
    strcpy(config->messages_directory, "messages"); // Default messages directory
    strcpy(config->alert_log_file, "logs/alerts.log"); // Default alert log file

    file = fopen(filename, "r");
    if (file == NULL) {
        log_message("ERROR", "Configuration file '%s' not found: %s", filename, strerror(errno));
        fprintf(stderr, "Error: Configuration file '%s' not found: %s\n", filename, strerror(errno));
        return -1;
    }

    log_message("INFO", "Loading configuration from file: %s", filename);

    while (fgets(line, sizeof(line), file)) {
        // Remove comments
        char* comment_pos = strchr(line, '#');
        if (comment_pos != NULL) {
            *comment_pos = '\0';
        }

        // Find '=' sign
        equals_pos = strchr(line, '=');
        if (equals_pos == NULL) {
            continue; // Line without '=', ignore
        }

        // Split key and value
        *equals_pos = '\0';
        key = trim(line);
        value = trim(equals_pos + 1);

        // Ignore empty lines
        if (strlen(key) == 0 || strlen(value) == 0) {
            continue;
        }

        // Parse different keys
        if (strcmp(key, "server_ip") == 0) {
            strncpy(config->server_ip, value, sizeof(config->server_ip) - 1);
            config->server_ip[sizeof(config->server_ip) - 1] = '\0';
            log_message("INFO", "Configuration loaded: server_ip = %s", config->server_ip);
        }
        else if (strcmp(key, "server_port") == 0) {
            config->server_port = atoi(value);
            log_message("INFO", "Configuration loaded: server_port = %d", config->server_port);
        }
        else if (strcmp(key, "ack_timeout") == 0) {
            config->ack_timeout = atoi(value);
            log_message("INFO", "Configuration loaded: ack_timeout = %d", config->ack_timeout);
        }
        else if (strcmp(key, "max_ack_size") == 0) {
            config->max_ack_size = atoi(value);
            log_message("INFO", "Configuration loaded: max_ack_size = %d", config->max_ack_size);
        }
        else if (strcmp(key, "max_buffer_size") == 0) {
            config->max_buffer_size = atoi(value);
            log_message("INFO", "Configuration loaded: max_buffer_size = %d", config->max_buffer_size);
        }
        else if (strcmp(key, "test_interval_hours") == 0) {
            config->test_interval_hours = atoi(value);
            log_message("INFO", "Configuration loaded: test_interval_hours = %d", config->test_interval_hours);
        }
        else if (strcmp(key, "health_check_enabled") == 0) {
            config->health_check_enabled = atoi(value);
            log_message("INFO", "Configuration loaded: health_check_enabled = %d", config->health_check_enabled);
        }
        else if (strcmp(key, "max_retry_attempts") == 0) {
            config->max_retry_attempts = atoi(value);
            log_message("INFO", "Configuration loaded: max_retry_attempts = %d", config->max_retry_attempts);
        }
        else if (strcmp(key, "retry_delay_seconds") == 0) {
            config->retry_delay_seconds = atoi(value);
            log_message("INFO", "Configuration loaded: retry_delay_seconds = %d", config->retry_delay_seconds);
        }
        else if (strcmp(key, "messages_directory") == 0) {
            // Validate the messages directory path for security
            if (strlen(value) >= sizeof(config->messages_directory)) {
                log_message("WARNING", "Messages directory path too long, using default");
                fprintf(stderr, "Warning: Messages directory path too long, using default\n");
            }
            else if (strstr(value, "..") != NULL) {
                log_message("WARNING", "Messages directory path contains directory traversal, using default");
                fprintf(stderr, "Warning: Messages directory path contains directory traversal, using default\n");
            }
            else {
                strncpy(config->messages_directory, value, sizeof(config->messages_directory) - 1);
                config->messages_directory[sizeof(config->messages_directory) - 1] = '\0';
                log_message("INFO", "Configuration loaded: messages_directory = %s", config->messages_directory);
            }
        }
        else if (strcmp(key, "alert_log_file") == 0) {
            // Validate the alert log file path for security
            if (strlen(value) >= sizeof(config->alert_log_file)) {
                log_message("WARNING", "Alert log file path too long, using default");
                fprintf(stderr, "Warning: Alert log file path too long, using default\n");
            }
            else if (strstr(value, "..") != NULL) {
                log_message("WARNING", "Alert log file path contains directory traversal, using default");
                fprintf(stderr, "Warning: Alert log file path contains directory traversal, using default\n");
            }
            else {
                strncpy(config->alert_log_file, value, sizeof(config->alert_log_file) - 1);
                config->alert_log_file[sizeof(config->alert_log_file) - 1] = '\0';
                log_message("INFO", "Configuration loaded: alert_log_file = %s", config->alert_log_file);
            }
        }
    }

    fclose(file);

    // Validate required configuration
    if (strlen(config->server_ip) == 0 || config->server_port == 0 ||
        config->ack_timeout == 0 || config->max_ack_size == 0 || config->max_buffer_size == 0) {
        log_message("ERROR", "Incomplete configuration found in file '%s'", filename);
        fprintf(stderr, "Error: Incomplete configuration found in file '%s'\n", filename);
        return -1;
    }

    // Validate ranges
    if (config->test_interval_hours < 1) config->test_interval_hours = 1;
    if (config->max_retry_attempts < 1) config->max_retry_attempts = 1;
    if (config->retry_delay_seconds < 1) config->retry_delay_seconds = 1;

    log_message("INFO", "Configuration successfully loaded from '%s'", filename);
    return 0;
}
