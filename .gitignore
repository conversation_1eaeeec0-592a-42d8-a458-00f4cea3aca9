# Testeur Automatisé pour Driver d'Automate de Biologie Médicale
# Fichier .gitignore pour Red Hat Enterprise Linux / Podman / iMX8 mini

# =============================================================================
# FICHIERS DE BUILD ET COMPILATION
# =============================================================================

# Répertoire de build CMake
build/
Build/
BUILD/

# Fichiers de build CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
compile_commands.json
CTestTestfile.cmake
_deps/

# Exécutables compilés
PPAEMD_Automates_simul
server_tester
automate_tester
*.exe
*.out

# Fichiers objets et bibliothèques
*.o
*.a
*.so
*.so.*
*.dylib
*.dll

# Fichiers de debug
*.dSYM/
*.su
*.idb
*.pdb

# =============================================================================
# LOGS ET FICHIERS TEMPORAIRES
# =============================================================================

# Logs applicatifs (générés automatiquement)
logs/
*.log
test_results.log

# Fichiers temporaires
*.tmp
*.temp
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Fichiers de sauvegarde
*.bak
*.backup
*.orig

# =============================================================================
# CONFIGURATION SENSIBLE
# =============================================================================

# Configuration de production avec données sensibles
# (garder server_tester.ini dans le repo mais attention aux secrets)
# server_tester.ini

# Fichiers de configuration locaux/personnalisés
server_tester.local.ini
server_tester.dev.ini
server_tester.prod.ini
config.local.*

# Certificats et clés (si utilisés dans le futur)
*.pem
*.key
*.crt
*.p12
*.pfx
secrets/
certs/

# =============================================================================
# ENVIRONNEMENT DE DÉVELOPPEMENT
# =============================================================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# JetBrains IDEs
.idea/
*.iws
*.iml
*.ipr

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# CONTENEURS ET DÉPLOIEMENT
# =============================================================================

# Images Docker/Podman locales (ne pas commiter les images)
*.tar
*.tar.gz
docker-compose.override.yml

# Volumes et données de conteneurs
volumes/
data/
var/

# Fichiers de déploiement locaux
.env
.env.local
.env.production
.env.development

# =============================================================================
# SYSTÈME ET OS
# =============================================================================

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Fichiers Zone.Identifier (Windows/WSL)
*:Zone.Identifier

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# =============================================================================
# TESTS ET DÉVELOPPEMENT
# =============================================================================

# Résultats de tests
test_results/
test_output/
coverage/
*.gcov
*.gcda
*.gcno

# Profiling
gmon.out
*.prof

# Valgrind
vgcore.*
*.memcheck

# =============================================================================
# DOCUMENTATION GÉNÉRÉE
# =============================================================================

# Documentation générée automatiquement
docs/build/
docs/_build/
site/

# LaTeX
*.aux
*.log
*.nav
*.out
*.snm
*.toc
*.vrb
*.fls
*.fdb_latexmk
*.synctex.gz

# =============================================================================
# SPÉCIFIQUE AU PROJET
# =============================================================================

# Fichiers de test temporaires (générés par test_suite.sh)
test_config.ini
test_invalid.ini

# Sauvegardes automatiques de configuration
*.ini.backup
config.backup.*

# Fichiers de monitoring temporaires
monitoring/
stats/
metrics/

# =============================================================================
# FICHIERS À GARDER (COMMENTÉS POUR RÉFÉRENCE)
# =============================================================================

# Ces fichiers DOIVENT être dans le repo :
# server_tester.c                  # Code source principal
# CMakeLists.txt                   # Configuration de build
# Dockerfile                       # Image Red Hat UBI9
# server_tester.ini                # Configuration de base (sans secrets)
# server_tester.example.ini        # Configuration d'exemple
# server_tester.redhat.ini         # Configuration Red Hat optimisée
# install_redhat.sh               # Script d'installation
# start_podman.sh                 # Script de gestion Podman
# test_suite.sh                   # Suite de tests
# messages/*.bin                  # Messages de test HL7/POCT
# messages/specifications/        # Documentation des messages
# README.md                       # Documentation principale
# README_REDHAT.md                # Guide Red Hat
# REDHAT_ADAPTATIONS.md           # Adaptations techniques
# PROJECT_STRUCTURE.md            # Structure du projet
