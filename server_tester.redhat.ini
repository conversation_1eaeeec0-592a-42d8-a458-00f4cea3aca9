# Configuration pour Red Hat Enterprise Linux avec Podman
# Testeur automatisé pour driver d'automate de biologie médicale sur iMX8 mini

[network]
# Adresse IP du conteneur EBMD ou du driver d'automate
# En environnement Red Hat, souvent sur réseau interne
server_ip = *************

# Port TCP du conteneur EBMD
# Port standard pour les automates de biologie médicale
server_port = 50901

[timeouts]
# Timeout pour l'accusé de réception (en secondes)
# Valeur conservative pour environnements industriels
ack_timeout = 10

[buffers]
# Taille maximale du buffer pour l'accusé de réception (en octets)
max_ack_size = 2048

# Taille maximale du buffer pour le fichier (en octets)
# Augmenté pour les messages HL7 complexes
max_buffer_size = 131072

[monitoring]
# Intervalle entre les tests périodiques (en heures)
# Configuration production pour surveillance continue
test_interval_hours = 2

# Activer les vérifications de santé (1 = activé, 0 = désactivé)
# Recommandé en production Red Hat
health_check_enabled = 1

# Nombre maximum de tentatives en cas d'échec
# Valeur élevée pour environnements critiques
max_retry_attempts = 5

# Délai entre les tentatives (en secondes)
# Délai plus long pour éviter la surcharge réseau
retry_delay_seconds = 10

# Répertoire contenant les messages de test
# Chemin absolu dans le conteneur
messages_directory = /opt/automate_tester/var/data/messages

# Fichier de log pour les alertes critiques
# Chemin absolu pour intégration avec les outils de monitoring Red Hat
alert_log_file = /opt/automate_tester/var/logs/alerts.log

# Configuration spécifique Red Hat Enterprise Linux

[redhat]
# Intégration avec systemd journal
use_systemd_journal = 1

# Niveau de log pour journald (DEBUG, INFO, WARNING, ERROR)
journal_log_level = INFO

# Intégration avec SELinux
selinux_enabled = 1

# Utilisation des capabilities Linux minimales
use_minimal_capabilities = 1

[security]
# Configuration de sécurité renforcée pour environnement industriel

# Validation stricte des certificats SSL/TLS (pour extensions futures)
strict_ssl_validation = 1

# Chiffrement des logs sensibles (pour extensions futures)
encrypt_sensitive_logs = 0

# Audit trail complet
enable_audit_trail = 1

[performance]
# Optimisations pour carte iMX8 mini

# Limitation mémoire (en MB)
max_memory_usage = 48

# Limitation CPU (pourcentage)
max_cpu_usage = 40

# Taille maximale des logs avant rotation (en MB)
max_log_size = 5

# Nombre de fichiers de log à conserver
max_log_files = 10

# Intervalle de nettoyage automatique (en heures)
cleanup_interval = 24

[integration]
# Intégration avec l'écosystème Red Hat

# Support RHEL Insights (pour monitoring avancé)
rhel_insights_enabled = 0

# Intégration avec Satellite (pour gestion centralisée)
satellite_integration = 0

# Support Ansible (pour automatisation)
ansible_facts_export = 1

# Métriques Prometheus (pour monitoring)
prometheus_metrics = 0

[alerts]
# Configuration des alertes pour environnement critique

# Seuil d'alerte pour échecs consécutifs
consecutive_failure_threshold = 3

# Seuil d'alerte pour taux d'échec (pourcentage)
failure_rate_threshold = 20

# Intervalle de vérification des seuils (en minutes)
alert_check_interval = 5

# Escalade automatique après X alertes
escalation_threshold = 10

# Notification par email (nécessite configuration SMTP)
email_notifications = 0

# Notification SNMP (pour intégration avec systèmes de monitoring)
snmp_notifications = 0

[maintenance]
# Configuration de maintenance automatique

# Nettoyage automatique des logs anciens
auto_cleanup_logs = 1

# Âge maximum des logs (en jours)
max_log_age_days = 30

# Vérification automatique des mises à jour
auto_update_check = 0

# Sauvegarde automatique de la configuration
auto_backup_config = 1

# Intervalle de sauvegarde (en heures)
backup_interval = 168

[development]
# Configuration pour environnement de développement
# (désactivé en production)

# Mode debug étendu
debug_mode = 0

# Logs détaillés des communications réseau
network_debug = 0

# Simulation de pannes pour tests
failure_simulation = 0

# Métriques de performance détaillées
detailed_metrics = 0

# Exemples de configuration par environnement :

# PRODUCTION CRITIQUE
# test_interval_hours = 1
# health_check_enabled = 1
# max_retry_attempts = 5
# retry_delay_seconds = 15
# consecutive_failure_threshold = 2

# PRODUCTION STANDARD
# test_interval_hours = 2
# health_check_enabled = 1
# max_retry_attempts = 3
# retry_delay_seconds = 10
# consecutive_failure_threshold = 3

# TEST/QUALIFICATION
# test_interval_hours = 0.5
# health_check_enabled = 1
# max_retry_attempts = 2
# retry_delay_seconds = 5
# consecutive_failure_threshold = 5

# DÉVELOPPEMENT
# test_interval_hours = 0.1
# health_check_enabled = 0
# max_retry_attempts = 1
# retry_delay_seconds = 2
# debug_mode = 1
