#!/bin/bash

# Suite de tests pour le testeur automatisé d'automate de biologie médicale

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_CONFIG="$SCRIPT_DIR/test_config.ini"
TEST_LOG="$SCRIPT_DIR/test_results.log"

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Compteurs de tests
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$TEST_LOG"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" >> "$TEST_LOG"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1" >> "$TEST_LOG"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1" >> "$TEST_LOG"
}

# Fonction pour exécuter un test
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    log "Exécution du test: $test_name"
    
    # Exécuter la commande et capturer le code de retour
    set +e
    eval "$test_command" >/dev/null 2>&1
    local result=$?
    set -e
    
    # Vérifier le résultat
    if [[ $result -eq $expected_result ]]; then
        log_success "Test réussi: $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "Test échoué: $test_name (attendu: $expected_result, obtenu: $result)"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Créer une configuration de test
create_test_config() {
    log "Création de la configuration de test..."
    
    cat > "$TEST_CONFIG" << EOF
# Configuration de test pour le testeur automatisé
server_ip = 127.0.0.1
server_port = 12345
ack_timeout = 2
max_ack_size = 512
max_buffer_size = 4096
test_interval_hours = 0.01
health_check_enabled = 0
max_retry_attempts = 1
retry_delay_seconds = 1
messages_directory = messages
alert_log_file = logs/test_alerts.log
EOF
    
    log_success "Configuration de test créée"
}

# Nettoyer les fichiers de test
cleanup_test_files() {
    log "Nettoyage des fichiers de test..."
    
    if [[ -f "$TEST_CONFIG" ]]; then
        rm "$TEST_CONFIG"
    fi
    
    if [[ -d "$SCRIPT_DIR/logs" ]]; then
        rm -f "$SCRIPT_DIR/logs/test_alerts.log"
    fi
    
    log_success "Nettoyage terminé"
}

# Test 1: Compilation
test_compilation() {
    log "=== Test 1: Compilation ==="
    
    # Nettoyer le répertoire de build
    if [[ -d "$SCRIPT_DIR/build" ]]; then
        rm -rf "$SCRIPT_DIR/build"
    fi
    
    # Tester la compilation
    run_test "Compilation avec CMake" \
        "cd '$SCRIPT_DIR' && mkdir build && cd build && cmake .. && make" \
        0
    
    # Vérifier que l'exécutable existe
    if [[ -f "$SCRIPT_DIR/build/PPAEMD_Automates_simul" ]]; then
        log_success "Exécutable créé avec succès"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_error "Exécutable non trouvé après compilation"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
}

# Test 2: Configuration
test_configuration() {
    log "=== Test 2: Configuration ==="
    
    create_test_config
    
    # Tester le chargement de configuration valide (échec de connexion attendu)
    run_test "Chargement configuration valide" \
        "cd '$SCRIPT_DIR' && ./build/PPAEMD_Automates_simul '$TEST_CONFIG'" \
        1  # échec de connexion attendu
    
    # Tester avec configuration inexistante
    run_test "Configuration inexistante" \
        "cd '$SCRIPT_DIR' && ./build/PPAEMD_Automates_simul --single nonexistent.ini" \
        1
}

# Test 3: Messages de test
test_messages() {
    log "=== Test 3: Messages de test ==="
    
    # Vérifier que le répertoire messages existe
    if [[ -d "$SCRIPT_DIR/messages" ]]; then
        local message_count=$(find "$SCRIPT_DIR/messages" -name "*.bin" | wc -l)
        if [[ $message_count -gt 0 ]]; then
            log_success "Messages de test trouvés: $message_count fichiers"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            log_error "Aucun message de test (.bin) trouvé"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    else
        log_error "Répertoire messages non trouvé"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    # Tester avec un message spécifique
    local test_message=$(find "$SCRIPT_DIR/messages" -name "*.bin" | head -1)
    if [[ -n "$test_message" ]]; then
        run_test "Test avec message spécifique" \
            "cd '$SCRIPT_DIR' && ./build/PPAEMD_Automates_simul '$test_message' '$TEST_CONFIG'" \
            1  # échec de connexion attendu
    fi
}

# Test 4: Scripts
test_scripts() {
    log "=== Test 4: Scripts ==="
    
    # Tester le script Podman
    run_test "Script Podman - aide" \
        "./start_podman.sh --help" \
        0

    # Tester le script d'installation Red Hat
    run_test "Script d'installation Red Hat - aide" \
        "./install_redhat.sh --help" \
        0

    # Vérifier les permissions
    if [[ -x "./start_podman.sh" ]]; then
        log_success "Script start_podman.sh exécutable"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_error "Script start_podman.sh non exécutable"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_TOTAL=$((TESTS_TOTAL + 1))

    if [[ -x "./install_redhat.sh" ]]; then
        log_success "Script install_redhat.sh exécutable"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_error "Script install_redhat.sh non exécutable"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
}

# Test 5: Docker
test_docker() {
    log "=== Test 5: Docker ==="
    
    # Vérifier que Docker est disponible
    if command -v docker &> /dev/null; then
        log_success "Docker disponible"
        
        # Tester la construction de l'image
        run_test "Construction image Docker" \
            "cd '$SCRIPT_DIR' && docker build -t automate-tester-test:latest ." \
            0
        
        # Nettoyer l'image de test
        docker rmi automate-tester-test:latest >/dev/null 2>&1 || true
        
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_warning "Docker non disponible, test ignoré"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    fi
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
}

# Test 6: Fichiers de configuration
test_config_files() {
    log "=== Test 6: Fichiers de configuration ==="
    
    # Vérifier les fichiers essentiels
    local essential_files=(
        "server_tester.c"
        "CMakeLists.txt"
        "server_tester.ini"
        "Dockerfile"
        "start_podman.sh"
        "install_redhat.sh"
        "README.md"
    )
    
    for file in "${essential_files[@]}"; do
        if [[ -f "$SCRIPT_DIR/$file" ]]; then
            log_success "Fichier trouvé: $file"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            log_error "Fichier manquant: $file"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
        TESTS_TOTAL=$((TESTS_TOTAL + 1))
    done
}

# Test 7: Validation de la configuration
test_config_validation() {
    log "=== Test 7: Validation de la configuration ==="
    
    # Créer une configuration invalide
    local invalid_config="$SCRIPT_DIR/invalid_config.ini"
    cat > "$invalid_config" << EOF
# Configuration invalide
server_ip = 
server_port = 0
EOF
    
    # Tester avec configuration invalide
    run_test "Configuration invalide" \
        "cd '$SCRIPT_DIR' && ./build/PPAEMD_Automates_simul --single '$invalid_config'" \
        1
    
    # Nettoyer
    rm "$invalid_config"
}

# Fonction principale
main() {
    log "=== Début de la suite de tests ==="
    
    # Initialiser le fichier de log
    echo "=== Suite de tests - $(date) ===" > "$TEST_LOG"
    
    # Exécuter les tests
    test_compilation
    test_configuration
    test_messages
    test_scripts
    test_docker
    test_config_files
    test_config_validation
    
    # Nettoyer
    cleanup_test_files
    
    # Afficher les résultats
    log "=== Résultats des tests ==="
    log "Tests total: $TESTS_TOTAL"
    log_success "Tests réussis: $TESTS_PASSED"
    
    if [[ $TESTS_FAILED -gt 0 ]]; then
        log_error "Tests échoués: $TESTS_FAILED"
    else
        log_success "Tests échoués: $TESTS_FAILED"
    fi
    
    local success_rate=$((TESTS_PASSED * 100 / TESTS_TOTAL))
    log "Taux de réussite: $success_rate%"
    
    # Code de retour
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log_success "Tous les tests sont passés avec succès!"
        return 0
    else
        log_error "Certains tests ont échoué. Consultez $TEST_LOG pour plus de détails."
        return 1
    fi
}

# Fonction d'aide
show_help() {
    echo "Suite de tests pour le testeur automatisé d'automate de biologie médicale"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --help    Afficher cette aide"
    echo ""
    echo "Cette suite de tests vérifie :"
    echo "  - La compilation du code C"
    echo "  - Le chargement de la configuration"
    echo "  - La présence des messages de test"
    echo "  - Le fonctionnement des scripts"
    echo "  - La construction Docker"
    echo "  - La validation des fichiers"
}

# Traitement des arguments
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
