# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/vscode/automate_tester

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/vscode/automate_tester/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/PPAEMD_Automates_simul.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/PPAEMD_Automates_simul.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/PPAEMD_Automates_simul.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/PPAEMD_Automates_simul.dir

# All Build rule for target.
CMakeFiles/PPAEMD_Automates_simul.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PPAEMD_Automates_simul.dir/build.make CMakeFiles/PPAEMD_Automates_simul.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PPAEMD_Automates_simul.dir/build.make CMakeFiles/PPAEMD_Automates_simul.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/vscode/automate_tester/build/CMakeFiles --progress-num=1,2 "Built target PPAEMD_Automates_simul"
.PHONY : CMakeFiles/PPAEMD_Automates_simul.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/PPAEMD_Automates_simul.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/vscode/automate_tester/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/PPAEMD_Automates_simul.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/vscode/automate_tester/build/CMakeFiles 0
.PHONY : CMakeFiles/PPAEMD_Automates_simul.dir/rule

# Convenience name for target.
PPAEMD_Automates_simul: CMakeFiles/PPAEMD_Automates_simul.dir/rule
.PHONY : PPAEMD_Automates_simul

# codegen rule for target.
CMakeFiles/PPAEMD_Automates_simul.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PPAEMD_Automates_simul.dir/build.make CMakeFiles/PPAEMD_Automates_simul.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/vscode/automate_tester/build/CMakeFiles --progress-num=1,2 "Finished codegen for target PPAEMD_Automates_simul"
.PHONY : CMakeFiles/PPAEMD_Automates_simul.dir/codegen

# clean rule for target.
CMakeFiles/PPAEMD_Automates_simul.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PPAEMD_Automates_simul.dir/build.make CMakeFiles/PPAEMD_Automates_simul.dir/clean
.PHONY : CMakeFiles/PPAEMD_Automates_simul.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

