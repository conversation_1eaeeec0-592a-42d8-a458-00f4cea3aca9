# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/vscode/automate_tester

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/vscode/automate_tester/build

# Include any dependencies generated for this target.
include CMakeFiles/PPAEMD_Automates_simul.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/PPAEMD_Automates_simul.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/PPAEMD_Automates_simul.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/PPAEMD_Automates_simul.dir/flags.make

CMakeFiles/PPAEMD_Automates_simul.dir/codegen:
.PHONY : CMakeFiles/PPAEMD_Automates_simul.dir/codegen

CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.o: CMakeFiles/PPAEMD_Automates_simul.dir/flags.make
CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.o: /home/<USER>/vscode/automate_tester/server_tester.c
CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.o: CMakeFiles/PPAEMD_Automates_simul.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/vscode/automate_tester/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.o"
	/usr/sbin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.o -MF CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.o.d -o CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.o -c /home/<USER>/vscode/automate_tester/server_tester.c

CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.i"
	/usr/sbin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/vscode/automate_tester/server_tester.c > CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.i

CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.s"
	/usr/sbin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/vscode/automate_tester/server_tester.c -o CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.s

# Object files for target PPAEMD_Automates_simul
PPAEMD_Automates_simul_OBJECTS = \
"CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.o"

# External object files for target PPAEMD_Automates_simul
PPAEMD_Automates_simul_EXTERNAL_OBJECTS =

PPAEMD_Automates_simul: CMakeFiles/PPAEMD_Automates_simul.dir/server_tester.c.o
PPAEMD_Automates_simul: CMakeFiles/PPAEMD_Automates_simul.dir/build.make
PPAEMD_Automates_simul: CMakeFiles/PPAEMD_Automates_simul.dir/compiler_depend.ts
PPAEMD_Automates_simul: CMakeFiles/PPAEMD_Automates_simul.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/vscode/automate_tester/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable PPAEMD_Automates_simul"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/PPAEMD_Automates_simul.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/PPAEMD_Automates_simul.dir/build: PPAEMD_Automates_simul
.PHONY : CMakeFiles/PPAEMD_Automates_simul.dir/build

CMakeFiles/PPAEMD_Automates_simul.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/PPAEMD_Automates_simul.dir/cmake_clean.cmake
.PHONY : CMakeFiles/PPAEMD_Automates_simul.dir/clean

CMakeFiles/PPAEMD_Automates_simul.dir/depend:
	cd /home/<USER>/vscode/automate_tester/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/vscode/automate_tester /home/<USER>/vscode/automate_tester /home/<USER>/vscode/automate_tester/build /home/<USER>/vscode/automate_tester/build /home/<USER>/vscode/automate_tester/build/CMakeFiles/PPAEMD_Automates_simul.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/PPAEMD_Automates_simul.dir/depend

