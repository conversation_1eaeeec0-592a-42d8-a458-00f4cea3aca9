#include "utils/common.h"
#include "utils/utils.h"
#include "utils/logging.h"
#include "utils/config.h"
#include "utils/network.h"
#include "utils/monitoring.h"

int main(int argc, char *argv[]) {
    Config config;
    TestMessage messages[MAX_MESSAGES];
    int message_count = 0;
    const char* config_file = CONFIG_FILE;
    time_t last_test_time = 0;
    time_t last_health_check = 0;
    time_t now;
    int test_interval_seconds;
    int i, test_result;
    int daemon_mode = 0;

    // Setup signal handlers
    setup_signal_handlers();

    // Check arguments
    if (argc < 1 || argc > 3) {
        fprintf(stderr, "Usage: %s [--daemon] [config_file]\n", argv[0]);
        fprintf(stderr, "  --daemon: Run in continuous monitoring mode\n");
        fprintf(stderr, "  config_file: Alternative configuration file (default: %s)\n", CONFIG_FILE);
        exit(1);
    }

    // Parse arguments
    for (i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--daemon") == 0) {
            daemon_mode = 1;
        } else {
            config_file = argv[i];
        }
    }

    // Load configuration first to get alert log path
    if (load_config(&config, config_file) != 0) {
        exit(1);
    }

    // Initialize logging system with alert log
    if (init_logging(config.alert_log_file) != 0) {
        fprintf(stderr, "Error: Failed to initialize logging system\n");
        exit(1);
    }

    log_message("INFO", "Automated Driver Tester started (daemon mode: %s)", daemon_mode ? "enabled" : "disabled");

    // Initialize statistics
    stats.start_time = time(NULL);

    // Display configuration
    printf("=== Configuration ===\n");
    printf("Server IP: %s\n", config.server_ip);
    printf("Server Port: %d\n", config.server_port);
    printf("ACK Timeout: %d seconds\n", config.ack_timeout);
    printf("Max ACK Size: %d bytes\n", config.max_ack_size);
    printf("Max Buffer Size: %d bytes\n", config.max_buffer_size);
    printf("Test Interval: %d hours\n", config.test_interval_hours);
    printf("Health Check: %s\n", config.health_check_enabled ? "enabled" : "disabled");
    printf("Max Retry Attempts: %d\n", config.max_retry_attempts);
    printf("Retry Delay: %d seconds\n", config.retry_delay_seconds);
    printf("Messages Directory: %s\n", config.messages_directory);
    printf("Alert Log File: %s\n", config.alert_log_file);
    printf("Daemon Mode: %s\n", daemon_mode ? "enabled" : "disabled");
    printf("====================\n\n");

    // Load test messages
    message_count = load_test_messages(config.messages_directory, messages, MAX_MESSAGES);
    if (message_count <= 0) {
        log_message("ERROR", "No test messages found in directory '%s'", config.messages_directory);
        fprintf(stderr, "Error: No test messages found in directory '%s'\n", config.messages_directory);
        close_logging();
        exit(1);
    }

    printf("Loaded %d test messages\n", message_count);
    log_message("INFO", "Loaded %d test messages for testing", message_count);

    // Calculate test interval in seconds
    test_interval_seconds = config.test_interval_hours * 3600;

    if (!daemon_mode) {
        // Single test mode - run all messages once
        printf("Running single test cycle...\n");
        log_message("INFO", "Running single test cycle with %d messages", message_count);

        int overall_success = 1;
        for (i = 0; i < message_count && keep_running; i++) {
            printf("Testing message %d/%d: %s\n", i + 1, message_count, messages[i].filename);
            test_result = perform_single_test(&config, &messages[i]);
            update_stats(test_result);

            if (!test_result) {
                overall_success = 0;
            }

            if (i < message_count - 1) {
                printf("Waiting 2 seconds before next test...\n");
                sleep(2);
            }
        }

        print_stats();
        log_message("INFO", "Single test cycle completed");

        if (!overall_success) {
            log_message("ERROR", "One or more tests failed in single test mode");
            close_logging();
            return 1;
        }
    } else {
        // Daemon mode - continuous monitoring
        printf("Starting continuous monitoring mode...\n");
        printf("Test interval: %d hours (%d seconds)\n", config.test_interval_hours, test_interval_seconds);
        printf("Health check: %s\n", config.health_check_enabled ? "enabled" : "disabled");
        printf("Press Ctrl+C to stop gracefully\n\n");

        log_message("INFO", "Starting continuous monitoring mode");
        log_message("INFO", "Test interval: %d hours, Health check: %s",
                   config.test_interval_hours, config.health_check_enabled ? "enabled" : "disabled");

        while (keep_running) {
            now = time(NULL);

            // Check if configuration should be reloaded
            if (reload_config) {
                log_message("INFO", "Reloading configuration...");
                if (load_config(&config, config_file) == 0) {
                    test_interval_seconds = config.test_interval_hours * 3600;
                    log_message("INFO", "Configuration reloaded successfully");
                    printf("Configuration reloaded\n");
                } else {
                    log_message("ERROR", "Failed to reload configuration, keeping current settings");
                }
                reload_config = 0;
            }

            // Perform scheduled tests
            if (now - last_test_time >= test_interval_seconds) {
                printf("\n=== Starting scheduled test cycle ===\n");
                log_message("INFO", "Starting scheduled test cycle");

                for (i = 0; i < message_count && keep_running; i++) {
                    printf("Testing message %d/%d: %s\n", i + 1, message_count, messages[i].filename);
                    test_result = perform_single_test(&config, &messages[i]);
                    update_stats(test_result);

                    // Small delay between tests to avoid overwhelming the server
                    if (i < message_count - 1 && keep_running) {
                        sleep(2);
                    }
                }

                last_test_time = now;
                print_stats();
                printf("=== Test cycle completed ===\n\n");
                log_message("INFO", "Scheduled test cycle completed");
            }

            // Perform health checks
            if (config.health_check_enabled && (now - last_health_check >= HEALTH_CHECK_INTERVAL)) {
                if (perform_health_check(&config)) {
                    log_message("INFO", "Health check passed");
                } else {
                    log_message("WARNING", "Health check failed - server may be unreachable");
                }
                last_health_check = now;
            }

            // Sleep for a short interval before checking again
            sleep(1);
        }

        printf("\nShutdown signal received, stopping gracefully...\n");
        log_message("INFO", "Shutdown signal received, stopping gracefully");
        print_stats();
    }

    log_message("INFO", "Automated Driver Tester shutting down");
    close_logging();
    return 0;
}
