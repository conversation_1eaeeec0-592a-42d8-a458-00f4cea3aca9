# Structure du Projet - Testeur Automatisé

## 📁 Structure Finale Optimisée

```
automate_tester/
├── 📄 README.md                     # Documentation principale
├── 📄 README_REDHAT.md              # Guide détaillé Red Hat/Podman
├── 📄 REDHAT_ADAPTATIONS.md         # Détail des adaptations techniques
├── 📄 PROJECT_STRUCTURE.md          # Ce fichier
│
├── 🔧 FICHIERS DE BUILD
├── server_tester.c                  # Code source principal (900+ lignes)
├── CMakeLists.txt                   # Configuration de build CMake
├── Dockerfile                       # Image multi-stage UBI9 pour Red Hat
│
├── ⚙️ CONFIGURATION
├── server_tester.ini                # Configuration de production
├── server_tester.example.ini        # Configuration d'exemple
├── server_tester.redhat.ini         # Configuration Red Hat optimisée
│
├── 🚀 SCRIPTS DE DÉPLOIEMENT
├── install_redhat.sh               # Installation automatisée Red Hat
├── start_podman.sh                 # Gestion Podman complète
├── test_suite.sh                   # Suite de tests (18 tests)
│
├── 📨 MESSAGES DE TEST
├── messages/
│   ├── m1.bin ... m8.bin           # Messages HL7/POCT de test
│   ├── m_query.bin                 # Message de requête
│   └── specifications/             # Documentation des messages
│       ├── message1.txt ... message8.txt
│       └── message_query.txt
│
├── 📊 LOGS ET BUILD
├── logs/                           # Logs applicatifs (générés)
│   ├── server_tester_YYYYMMDD.log
│   └── alerts.log
└── build/                          # Répertoire de build (généré)
    ├── PPAEMD_Automates_simul      # Exécutable compilé
    └── [fichiers CMake...]
```

## 🗂️ Description des Fichiers

### 📄 Documentation
- **`README.md`** : Documentation principale avec installation rapide
- **`README_REDHAT.md`** : Guide complet Red Hat/Podman/SELinux
- **`REDHAT_ADAPTATIONS.md`** : Détail technique des adaptations
- **`PROJECT_STRUCTURE.md`** : Structure du projet (ce fichier)

### 🔧 Code Source et Build
- **`server_tester.c`** : Code source principal avec toutes les fonctionnalités
- **`CMakeLists.txt`** : Configuration de build CMake
- **`Dockerfile`** : Image multi-stage optimisée UBI9 Red Hat

### ⚙️ Configuration
- **`server_tester.ini`** : Configuration de production active
- **`server_tester.example.ini`** : Modèle de configuration avec exemples
- **`server_tester.redhat.ini`** : Configuration Red Hat avec paramètres avancés

### 🚀 Scripts de Déploiement
- **`install_redhat.sh`** : Installation complète automatisée (une commande)
- **`start_podman.sh`** : Gestion Podman (build, run, stop, logs, status)
- **`test_suite.sh`** : Suite de tests complète (18 tests, 100% réussite)

### 📨 Messages de Test
- **`messages/*.bin`** : Messages binaires HL7/POCT pour les tests
- **`messages/specifications/`** : Documentation des formats de messages

## 🎯 Fichiers Supprimés (Simplification)

### ❌ Fichiers Inutiles Supprimés
- **`docker-compose.yml`** : Remplacé par `install_redhat.sh` + systemd
- **`start_tester.sh`** : Remplacé par `start_podman.sh` (spécifique Red Hat)
- **`install.sh`** : Remplacé par `install_redhat.sh` (optimisé Red Hat)
- **`automate-tester.service`** : Généré automatiquement par `install_redhat.sh`
- **`IMPROVEMENTS.md`** : Intégré dans `REDHAT_ADAPTATIONS.md`
- **`*.Zone.Identifier`** : Fichiers Windows inutiles
- **`m1.bin`** : Doublon (déjà dans `messages/`)

### 💡 Justification des Suppressions

1. **`docker-compose.yml`** ❌
   - **Pourquoi supprimé** : Redondant avec les scripts Red Hat
   - **Remplacé par** : `install_redhat.sh` + service systemd natif
   - **Avantage** : Intégration native Red Hat, plus simple

2. **Scripts génériques** ❌
   - **Pourquoi supprimés** : Non optimisés pour Red Hat/Podman
   - **Remplacés par** : Scripts spécialisés Red Hat
   - **Avantage** : SELinux, systemd, optimisations iMX8

3. **Fichiers de service manuels** ❌
   - **Pourquoi supprimés** : Générés automatiquement
   - **Remplacés par** : Génération automatique dans `install_redhat.sh`
   - **Avantage** : Configuration dynamique, moins d'erreurs

## 🚀 Workflow d'Utilisation

### 1. Installation (Une seule fois)
```bash
git clone <repository>
cd automate_tester
sudo ./install_redhat.sh
```

### 2. Configuration
```bash
sudo nano /opt/automate_tester/server_tester.ini
```

### 3. Démarrage
```bash
sudo systemctl start automate-tester-podman
sudo systemctl enable automate-tester-podman
```

### 4. Monitoring
```bash
sudo journalctl -u automate-tester-podman -f
./start_podman.sh --status
```

### 5. Maintenance
```bash
sudo ./install_redhat.sh --update
./start_podman.sh --build
```

## 📊 Métriques du Projet

### Code Source
- **Lignes de code C** : ~900 lignes
- **Fonctionnalités** : 7 principales + robustesse
- **Tests** : 18 tests automatisés (100% réussite)

### Scripts
- **Installation** : 1 commande (`install_redhat.sh`)
- **Gestion** : 1 script (`start_podman.sh`)
- **Tests** : 1 suite (`test_suite.sh`)

### Configuration
- **Fichiers** : 3 configurations (production, exemple, Red Hat)
- **Paramètres** : ~20 paramètres configurables
- **Environnements** : Production, test, développement

### Documentation
- **Fichiers** : 4 fichiers de documentation
- **Pages** : ~50 pages de documentation complète
- **Guides** : Installation, utilisation, dépannage

## ✅ Avantages de la Structure Finale

1. **Simplicité** : Moins de fichiers, plus de clarté
2. **Spécialisation** : Optimisé pour Red Hat/Podman/iMX8
3. **Automatisation** : Installation en une commande
4. **Maintenance** : Scripts de gestion intégrés
5. **Documentation** : Guides complets et spécialisés
6. **Sécurité** : SELinux, conteneurs rootless, images UBI
7. **Performance** : Optimisations iMX8 mini
8. **Conformité** : Standards Red Hat Enterprise

Le projet est maintenant **parfaitement structuré** pour votre environnement Red Hat avec Podman sur carte iMX8 mini ! 🎯
